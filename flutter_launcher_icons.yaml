# flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  image_path: "assets/icon/icon.png"
  android: "launcher_icon"
  image_path_android: "assets/icon/icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  # adaptive_icon_background: "assets/icon/background.png"
  # adaptive_icon_foreground: "assets/icon/foreground.png"
  # adaptive_icon_monochrome: "assets/icon/monochrome.png"
  ios: true
  image_path_ios: "assets/icon/icon.png"
  remove_alpha_channel_ios: true
  image_path_ios_dark_transparent: "assets/icon/icon_t.png"
  image_path_ios_tinted_grayscale: "assets/icon/icon_t.png"
  desaturate_tinted_to_grayscale_ios: true

  # web:
  #   generate: true
  #   image_path: "path/to/image.png"
  #   background_color: "#hexcode"
  #   theme_color: "#hexcode"

  # windows:
  #   generate: true
  #   image_path: "path/to/image.png"
  #   icon_size: 48 # min:48, max:256, default: 48

  # macos:
  #   generate: true
  #   image_path: "path/to/image.png"
