import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:nanami_flutter/common/utils/color_utils.dart';

// Github 风格的 ActionItem
class GithubActionItem extends StatelessWidget {
  final IconData? icon;
  final String? text;
  final String? badgeText; // 可选的徽章文本
  final VoidCallback? onPressed;
  final bool showDropdownArrow; // 是否显示下拉箭头
  final Color? backgroundColor; // 按钮背景色，可空以便根据主题自动选择
  final Color? borderColor; // 边框颜色，可空以便根据主题自动选择
  final Color? iconColor; // 图标颜色，可空以便根据主题自动选择
  final Color? textColor; // 文本颜色，可空以便根据主题自动选择
  final Color? dropdownArrowColor; // 下拉箭头颜色，可空以便根据主题自动选择
  final Color badgeBackgroundColor; // 徽章背景色
  final Color badgeTextColor; // 徽章文本颜色
  final double textSize; // 文本大小
  final double iconSize; // 图标大小

  const GithubActionItem({
    super.key,
    this.icon,
    this.text,
    this.badgeText,
    this.onPressed,
    this.showDropdownArrow = false, // 默认为false
    this.backgroundColor, // 按钮背景色，为null时根据主题自动选择
    this.borderColor, // 边框颜色，为null时根据主题自动选择
    this.iconColor, // 图标颜色，为null时根据主题自动选择
    this.textColor, // 文本颜色，为null时根据主题自动选择
    this.dropdownArrowColor, // 下拉箭头颜色，为null时根据主题自动选择
    this.badgeBackgroundColor = Colors.white, // 徽章背景色
    this.badgeTextColor = CupertinoColors.systemBlue, // 徽章文本颜色
    this.textSize = 15.0, // 默认文本大小
    this.iconSize = 15.0, // 默认图标大小
  });

  @override
  Widget build(BuildContext context) {
    // 定义动态颜色
    final CupertinoDynamicColor defaultBgColor =
        CupertinoDynamicColor.withBrightness(
          color: CupertinoColors.systemGrey6.withOpacity(0.5),
          darkColor: CupertinoColors.tertiarySystemFill.withOpacity(0.3),
        );

    final CupertinoDynamicColor defaultTextColor =
        CupertinoDynamicColor.withBrightness(
          color: Color(ColorUtil.fromHex('434248')),
          darkColor: Colors.white,
        );

    // 注释的边框颜色保留注释
    // final CupertinoDynamicColor defaultBorderColor = CupertinoDynamicColor.withBrightness(
    //   color: Color(ColorUtil.fromHex('eaeaea')),
    //   darkColor: Colors.white12,
    // );

    final CupertinoDynamicColor defaultArrowColor =
        CupertinoDynamicColor.withBrightness(
          color: Colors.black26,
          darkColor: Colors.white30,
        );

    // 处理颜色解析
    Color resolveColor(Color? color, Color defaultDynamicColor) {
      if (color == null) {
        return defaultDynamicColor;
      }
      if (color is CupertinoDynamicColor) {
        return color.resolveFrom(context);
      }
      return color;
    }

    final Color bgColor = resolveColor(
      backgroundColor,
      defaultBgColor.resolveFrom(context),
    );
    final Color textAndIconColor = resolveColor(
      textColor,
      defaultTextColor.resolveFrom(context),
    );
    // final Color borderCol = resolveColor(borderColor, defaultBorderColor.resolveFrom(context));
    final Color arrowColor = resolveColor(
      dropdownArrowColor,
      defaultArrowColor.resolveFrom(context),
    );

    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero, // 移除默认内边距
      minSize: 0, // 移除最小尺寸限制
      child: Container(
        padding: const EdgeInsets.symmetric(
          // 默认是 7，2
          horizontal: 10.0,
          vertical: 6,
        ), // 调整内边距以匹配参考
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(50.0), // 圆角
          // border: Border.all(color: borderCol, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min, // 让 Row 包裹内容
          crossAxisAlignment: CrossAxisAlignment.center, // 显式确保垂直居中
          children: [
            // 显示图标（如果提供）
            if (icon != null)
              Icon(
                icon,
                color: resolveColor(iconColor, textAndIconColor),
                size: iconSize,
              ),
            // 图标和文本之间的间距
            if (icon != null && (text != null || badgeText != null))
              const SizedBox(width: 4),

            // 显示文本（如果提供）
            if (text != null)
              Text(
                text!,
                style: TextStyle(
                  fontSize: textSize, // 使用传入的文本大小
                  color: resolveColor(
                    textColor,
                    textAndIconColor,
                  ), // 使用传入的颜色或根据主题自动选择
                  // fontWeight: FontWeight.bold,
                ),
              ),
            // 文本和徽章之间的间距
            if (text != null && badgeText != null) const SizedBox(width: 4),

            // 显示徽章（如果提供）
            if (badgeText != null)
              Container(
                margin: const EdgeInsets.only(left: 2), // 与左侧元素的间距
                padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 1),
                decoration: BoxDecoration(
                  color: badgeBackgroundColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  badgeText!,
                  style: TextStyle(
                    color: badgeTextColor,
                    fontSize: textSize,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

            // 显示下拉箭头（如果需要）
            if (showDropdownArrow)
              Padding(
                padding: const EdgeInsets.only(left: 2.0), // 箭头与左侧元素的间距
                child: Icon(
                  CupertinoIcons.chevron_down,
                  color: arrowColor,
                  size: iconSize,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
