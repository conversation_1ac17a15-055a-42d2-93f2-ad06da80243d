import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pull_down_button/pull_down_button.dart';

class OverlayWatermarkTileItem extends StatelessWidget {
  // 图片标题
  final String title;
  // 图片路径
  final String imagePath;
  // 图片点击回调
  final VoidCallback? onImageTap;
  // 标题长按回调
  final VoidCallback? onTitleLongPressed;
  // 重命名回调
  final VoidCallback? onRename;
  // 删除回调
  final VoidCallback? onDelete;

  const OverlayWatermarkTileItem({
    super.key,
    required this.title,
    required this.imagePath,
    this.onImageTap,
    this.onDelete,
    this.onTitleLongPressed,
    this.onRename,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoListTile.notched(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
      leading: Material(
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        clipBehavior: Clip.antiAlias,
        child: GestureDetector(
          onTap: onImageTap,
          child: Image.file(
            File(imagePath),
            width: 70,
            height: 70,
            fit: BoxFit.cover,
          ),
        ),
      ),
      leadingSize: 70,
      // 标题
      title: GestureDetector(
        onLongPress: onTitleLongPressed,
        child: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      ),

      // 操作按钮
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 下拉菜单
          PullDownButton(
            itemBuilder:
                (context) => [
                  PullDownMenuTitle(title: Text('图片操作')),

                  PullDownMenuItem(
                    onTap: onRename,
                    title: '重命名',
                    icon: CupertinoIcons.pencil,
                  ),

                  PullDownMenuItem(
                    onTap: onDelete,
                    title: '移除',
                    isDestructive: true,
                    icon: CupertinoIcons.delete,
                  ),
                ],
            buttonBuilder:
                (context, showMenu) => CupertinoButton(
                  onPressed: showMenu,
                  padding: EdgeInsets.zero,
                  child: const Icon(CupertinoIcons.ellipsis),
                ),
          ),
        ],
      ),
    );
  }
}
