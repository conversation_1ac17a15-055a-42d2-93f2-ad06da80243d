import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/add_image_controller.dart';
import 'package:nanami_flutter/common/controllers/image_overlay_controller.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/utils/human_face_detect.dart';
import 'package:nanami_flutter/widgets/extended_image_gesture_page_view_widget_for_overlay.dart';
import 'package:nanami_flutter/widgets/show_mask_setting_sheet.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:toastification/toastification.dart';

class OverlayImagesPreview extends StatelessWidget {
  // 初始索引
  final int initIndex;
  // 所有图片列表
  final List<OverlayImage> overlayImages;

  // 控制器
  late final ExtendedPageController _pageController;
  // 当前图片
  late final Rx<OverlayImage> _currentImage;
  // 设置控制器
  final SettingController _settingController = Get.find<SettingController>();

  // 构造函数
  OverlayImagesPreview({
    super.key,
    required this.initIndex,
    required this.overlayImages,
  }) {
    // 在构造函数体中初始化_pageController
    _pageController = ExtendedPageController(initialPage: initIndex);
    // 初始化当前图片
    _currentImage = overlayImages[initIndex].obs;
  }

  // 显示预览设置
  void _showPreviewSettings(BuildContext context) {
    showShowMaskSettingSheet(context);
  }

  // 识别真人脸部
  void _detectHumanFace() async {
    // 获取当前图片
    final OverlayImage overlayImage = _currentImage.value;

    // 显示加载提示
    final toast = toastification.show(
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      title: Text("正在识别脸部"),
      description: Text("请耐心等待..."),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 4),
      closeButton: ToastCloseButton(showType: CloseButtonShowType.none),
      closeOnClick: false,
      pauseOnHover: false,
    );

    try {
      // 调用人脸检测方法
      String? path = await HumanFaceDetectUtils.getAutoFaceContoursMask(
        overlayImage.filePath,
        expansionRate: _settingController.faceDetectExpansionRate.value,
      );

      // 关闭加载提示
      toastification.dismiss(toast);

      // 只有在成功识别到人脸时才设置蒙版路径
      if (path != null) {
        // 更新蒙版路径
        overlayImage.maskFilePath?.value = path;

        // 强制刷新当前图片
        _currentImage.refresh();

        // 显示成功提示
        toastification.show(
          type: ToastificationType.success,
          style: ToastificationStyle.flat,
          title: Text("识别成功"),
          description: Text("已标注脸部"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
      } else {
        // 显示警告提示
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          title: Text("识别失败"),
          description: Text("未检测到人脸"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
      }
    } catch (e) {
      // 关闭加载提示
      toastification.dismiss(toast);

      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("识别失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
    }
  }

  // 手动标注功能
  void _manualMark(BuildContext context) {
    // 获取当前图片
    // final OverlayImage overlayImage = _currentImage.value;
  }

  // 移除当前图片
  void _removeCurrentImage() {
    // 获取当前图片索引
    final int currentIndex = _pageController.page?.round() ?? initIndex;

    // 如果只有一张图片，直接返回上一页
    if (overlayImages.length <= 1) {
      Get.back();

      // 延迟一下再移除图片，确保已经返回上一页
      Future.delayed(const Duration(milliseconds: 100), () {
        // 获取 AddImageController 实例
        final AddImageController controller = Get.find<AddImageController>();
        // 移除图片
        controller.removePendingImageByIndex(currentIndex);
      });
      return;
    }

    // 获取 ImageOverlayController 实例
    final ImageOverlayController controller =
        Get.find<ImageOverlayController>();

    // 返回上一页
    Get.back();

    // 移除图片
    controller.removeOverlayImageByIndex(currentIndex);
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        backgroundColor: CupertinoColors.transparent,
        padding: const EdgeInsetsDirectional.all(0),
        middle: Obx(() => Text(_currentImage.value.name)),
        // 右上角操作按钮
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 眼睛图标
            CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: () => _showPreviewSettings(context),
              child: const Icon(CupertinoIcons.eye, size: 24.0),
            ),
            PullDownButton(
              itemBuilder:
                  (context) => [
                    PullDownMenuItem(
                      onTap: () => _detectHumanFace(),
                      title: '识别真人',
                      icon: CupertinoIcons.viewfinder,
                    ),
                    PullDownMenuItem(
                      onTap: () {
                        // 暂未实现动漫识别功能
                        toastification.show(
                          context: context,
                          type: ToastificationType.info,
                          style: ToastificationStyle.flat,
                          title: Text("功能开发中"),
                          description: Text("动漫识别功能即将上线"),
                          alignment: Alignment.topCenter,
                          autoCloseDuration: const Duration(seconds: 2),
                          dragToClose: true,
                        );
                      },
                      title: '识别动漫',
                      icon: CupertinoIcons.viewfinder,
                    ),
                    PullDownMenuItem(
                      onTap: () => _manualMark(context),
                      title: '手动标注',
                      icon: CupertinoIcons.pencil,
                    ),
                    PullDownMenuDivider.large(),
                    PullDownMenuItem(
                      onTap: () => _removeCurrentImage(),
                      title: '移除',
                      isDestructive: true,
                      icon: CupertinoIcons.delete,
                    ),
                  ],
              buttonBuilder:
                  (context, showMenu) => CupertinoButton(
                    onPressed: showMenu,
                    padding: EdgeInsets.zero,
                    child: const Icon(
                      CupertinoIcons.ellipsis_circle,
                      size: 24.0,
                    ),
                  ),
            ),
          ],
        ),
      ),
      child: Obx(() {
        // 直接使用设置控制器中的值
        final blendMode = _settingController.showMaskBlendMode.value;
        final opacity = _settingController.showMaskOpacity.value;

        return ExtendedImageGesturePageView.builder(
          controller: _pageController,
          itemCount: overlayImages.length,
          itemBuilder: (context, index) {
            // 构造图片
            return ExtendedImageGesturePageViewWidgetForOverlay(
              overlayImage: overlayImages[index],
              maskBlendMode: blendMode,
              maskOpacity: opacity,
            );
          },
          // 改变时更新当前图片
          onPageChanged: (value) {
            _currentImage.value = overlayImages[value];
          },
        );
      }),
    );
  }
}
