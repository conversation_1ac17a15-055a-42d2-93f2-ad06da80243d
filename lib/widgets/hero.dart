import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';

/// 改进 Hero 动画，使滑动退出页面时更顺滑
/// 这是对标准 Hero 组件的增强封装，主要解决在滑动退出页面时的动画效果问题
class HeroWidget extends StatefulWidget {
  const HeroWidget({
    required this.child,
    required this.tag,
    required this.slidePagekey,
    this.slideType = SlideType.onlyImage,
  });

  /// 需要应用 Hero 效果的子组件
  final Widget child;

  /// 滑动类型，默认为 onlyImage
  /// onlyImage: 仅图片部分可以滑动
  /// wholePage: 整个页面可以滑动
  final SlideType slideType;

  /// Hero 动画的标签，用于在不同页面间识别相同的元素
  final Object tag;

  /// 滑动页面的 Key，用于控制滑动状态
  final GlobalKey<ExtendedImageSlidePageState> slidePagekey;

  @override
  _HeroWidgetState createState() => _HeroWidgetState();
}

class _HeroWidgetState extends State<HeroWidget> {
  /// 矩形补间动画，用于在 Hero 动画中控制元素大小和位置的变化
  RectTween? _rectTween;

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: widget.tag,

      /// 创建矩形补间，记录起始和结束的矩形区域
      createRectTween: (Rect? begin, Rect? end) {
        _rectTween = RectTween(begin: begin, end: end);
        return _rectTween!;
      },

      /// 自定义 Hero 飞行过程中的构建器
      /// 在滑动退出时使 Hero 动画更平滑
      flightShuttleBuilder: (BuildContext flightContext,
          Animation<double> animation,
          HeroFlightDirection flightDirection,
          BuildContext fromHeroContext,
          BuildContext toHeroContext) {
        // 根据飞行方向选择适当的 Hero 组件
        final Hero hero = (flightDirection == HeroFlightDirection.pop
            ? fromHeroContext.widget
            : toHeroContext.widget) as Hero;
        if (_rectTween == null) {
          return hero;
        }

        if (flightDirection == HeroFlightDirection.pop) {
          /// 判断是否需要修复变换
          /// 当滑动类型为 onlyImage 且当前有位移或缩放时需要修复
          final bool fixTransform = widget.slideType == SlideType.onlyImage &&
              (widget.slidePagekey.currentState!.offset != Offset.zero ||
                  widget.slidePagekey.currentState!.scale != 1.0);

          final Widget toHeroWidget = (toHeroContext.widget as Hero).child;
          return AnimatedBuilder(
            animation: animation,
            builder: (BuildContext buildContext, Widget? child) {
              Widget animatedBuilderChild = hero.child;

              /// 创建更平滑的过渡效果
              /// 通过叠加两个 Widget 并调整透明度实现淡入淡出效果
              animatedBuilderChild = Stack(
                clipBehavior: Clip.antiAlias,
                alignment: Alignment.center,
                children: <Widget>[
                  /// 目标 Widget，随着动画进行逐渐淡出
                  Opacity(
                    opacity: 1 - animation.value,
                    child: UnconstrainedBox(
                      child: SizedBox(
                        width: _rectTween!.begin!.width,
                        height: _rectTween!.begin!.height,
                        child: toHeroWidget,
                      ),
                    ),
                  ),

                  /// 当前 Widget，随着动画进行逐渐淡入
                  Opacity(
                    opacity: animation.value,
                    child: animatedBuilderChild,
                  )
                ],
              );

              /// 在滑动退出时修复变换
              /// 应用滑动页面的位移和缩放效果
              if (fixTransform) {
                final Tween<Offset> offsetTween = Tween<Offset>(
                    begin: Offset.zero,
                    end: widget.slidePagekey.currentState!.offset);

                final Tween<double> scaleTween = Tween<double>(
                    begin: 1.0, end: widget.slidePagekey.currentState!.scale);
                animatedBuilderChild = Transform.translate(
                  offset: offsetTween.evaluate(animation),
                  child: Transform.scale(
                    scale: scaleTween.evaluate(animation),
                    child: animatedBuilderChild,
                  ),
                );
              }

              return animatedBuilderChild;
            },
          );
        }
        return hero.child;
      },
      child: widget.child,
    );
  }
}
