import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:blendmask/blendmask.dart';
import 'package:flutter/material.dart';
import 'package:pull_down_button/pull_down_button.dart';

class ImageTileItem extends StatelessWidget {
  // 图片标题
  final String title;
  // 图片路径
  final String imagePath;
  // 蒙版路径
  final String? maskPath;
  // 蒙版混合模式
  final BlendMode maskBlendMode;
  // 蒙版不透明度
  final double maskOpacity;
  // 标题长按回调
  final VoidCallback? onTitleLongPressed;
  // 图片点击回调
  final VoidCallback? onImageTap;
  // 人脸识别回调
  final VoidCallback? onDetectHumanFace;
  // 动漫脸识别回调
  final VoidCallback? onDetectAnimeFace;
  // 手动标注回调
  final VoidCallback? onManualMark;
  // 删除回调
  final VoidCallback? onDelete;

  const ImageTileItem({
    super.key,
    required this.title,
    required this.imagePath,
    required this.maskPath,
    required this.maskBlendMode,
    required this.maskOpacity,
    this.onDelete,
    this.onTitleLongPressed,
    this.onImageTap,
    this.onDetectHumanFace,
    this.onDetectAnimeFace,
    this.onManualMark,
  });

  // 构建子标题
  Widget _buildSubtitle(BuildContext context) {
    if (maskPath == null) {
      return Text('未标注');
    } else {
      return Text(
        '已标注',
        style: TextStyle(
          color: CupertinoTheme.of(context).primaryColor,
          fontWeight: FontWeight.bold,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoListTile.notched(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
      leading: Material(
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        clipBehavior: Clip.antiAlias,
        child: GestureDetector(
          onTap: onImageTap,
          child: Stack(
            children: [
              // 底图
              Image.file(
                File(imagePath),
                width: 70,
                height: 70,
                fit: BoxFit.cover,
              ),
              // 遮罩 - 使用multiply混合模式
              if (maskPath != null)
                BlendMask(
                  key: ValueKey("$maskOpacity-$maskBlendMode"),
                  blendMode: maskBlendMode,
                  opacity: maskOpacity,
                  child: Image.file(
                    File(maskPath ?? ''),
                    width: 70,
                    height: 70,
                    fit: BoxFit.cover,
                  ),
                ),
              // Text("不透明度：$maskOpacity"),
            ],
          ),
        ),
      ),
      leadingSize: 70,
      // 标题
      title: GestureDetector(
        onLongPress: onTitleLongPressed,
        child: Text(title),
      ),
      // 副标题
      subtitle: _buildSubtitle(context),
      // 操作按钮
      trailing: PullDownButton(
        itemBuilder:
            (context) => [
              PullDownMenuTitle(title: Text('图片操作')),
              PullDownMenuActionsRow.medium(
                items: [
                  PullDownMenuItem(
                    onTap: onDetectHumanFace,
                    title: '识别真人',
                    icon: CupertinoIcons.viewfinder,
                  ),
                  PullDownMenuItem(
                    onTap: () {},
                    title: '识别动漫',
                    icon: CupertinoIcons.viewfinder,
                  ),
                  PullDownMenuItem(
                    onTap: () {},
                    title: '手动标注',
                    icon: CupertinoIcons.pencil,
                  ),
                ],
              ),
              PullDownMenuItem(
                onTap: onDelete,
                title: '移除',
                isDestructive: true,
                icon: CupertinoIcons.delete,
              ),
            ],
        buttonBuilder:
            (context, showMenu) => CupertinoButton(
              onPressed: showMenu,
              padding: EdgeInsets.zero,
              child: const Icon(CupertinoIcons.ellipsis),
            ),
      ),
    );
  }
}
