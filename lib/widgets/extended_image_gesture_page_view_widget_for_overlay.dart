import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:nanami_flutter/common/controllers/image_overlay_controller.dart';

/// 扩展图片手势页面视图组件
/// 用于在图片上叠加其他元素，并让它们跟随图片一起缩放
class ExtendedImageGesturePageViewWidgetForOverlay extends StatefulWidget {
  final OverlayImage overlayImage;
  // 蒙版混合模式
  final BlendMode maskBlendMode;
  // 蒙版不透明度
  final double maskOpacity;

  const ExtendedImageGesturePageViewWidgetForOverlay({
    super.key,
    required this.overlayImage,
    this.maskBlendMode = BlendMode.multiply,
    this.maskOpacity = 0.5,
  });

  @override
  State<ExtendedImageGesturePageViewWidgetForOverlay> createState() =>
      _ExtendedImageGesturePageViewWidgetState();
}

class _ExtendedImageGesturePageViewWidgetState
    extends State<ExtendedImageGesturePageViewWidgetForOverlay> {
  // 蒙版图片
  ui.Image? _maskImage;
  // 是否正在加载蒙版图片
  bool _isLoadingMask = false;
  // 当前加载的蒙版路径，用于避免重复加载
  String? _currentLoadingMaskPath;

  @override
  void initState() {
    super.initState();
    // 如果有蒙版路径，加载蒙版图片
    _loadMaskImage();
  }

  @override
  void didUpdateWidget(ExtendedImageGesturePageViewWidgetForOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果蒙版路径发生变化，且与当前加载的路径不同，在下一帧重新加载蒙版图片
    final String? newMaskPath = widget.overlayImage.maskFilePath.value;
    if (newMaskPath != oldWidget.overlayImage.maskFilePath.value &&
        newMaskPath != _currentLoadingMaskPath &&
        !_isLoadingMask) {
      // 使用 addPostFrameCallback 确保在构建完成后执行
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _loadMaskImage();
        }
      });
    }
  }

  // 加载蒙版图片
  void _loadMaskImage() {
    final String? maskPath = widget.overlayImage.maskFilePath.value;

    // 如果没有蒙版路径或者正在加载，或者当前路径与正在加载的路径相同，直接返回
    if (maskPath == null ||
        _isLoadingMask ||
        maskPath == _currentLoadingMaskPath) {
      return;
    }

    // 设置为正在加载状态，并记录当前加载的路径
    setState(() {
      _isLoadingMask = true;
      _maskImage = null;
      _currentLoadingMaskPath = maskPath;
    });

    // 加载蒙版图片
    final File maskFile = File(maskPath);
    if (maskFile.existsSync()) {
      // 读取文件字节
      final Uint8List bytes = maskFile.readAsBytesSync();
      // 解码图片
      ui.decodeImageFromList(bytes, (ui.Image image) {
        if (mounted) {
          setState(() {
            _maskImage = image;
            _isLoadingMask = false;
          });
        }
      });
    } else {
      // 文件不存在，设置为未加载状态
      setState(() {
        _isLoadingMask = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用 Obx 监听 filePath 和 maskFilePath 的变化
    return Obx(() {
      // 访问 maskFilePath 以便监听其变化
      final String? maskPath = widget.overlayImage.maskFilePath.value;

      // 如果有新的蒙版路径，且与当前加载的路径不同，在构建完成后重新加载蒙版图片
      if (maskPath != null &&
          maskPath != _currentLoadingMaskPath &&
          !_isLoadingMask) {
        // 使用 addPostFrameCallback 确保在构建完成后执行
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _loadMaskImage();
          }
        });
      }

      return ExtendedImage.file(
        File(widget.overlayImage.filePath),
        fit: BoxFit.contain,
        enableSlideOutPage: true, // 启用滑动退出页面
        mode: ExtendedImageMode.gesture, // 启用手势模式
        initGestureConfigHandler: (ExtendedImageState state) {
          // 手势配置
          return GestureConfig(
            // 必须设置inPageView为true，以便在ExtendedImageGesturePageView中使用
            inPageView: true,
            minScale: 1.0, // 最小缩放比例
            initialScale: 1.0, // 初始缩放比例
            maxScale: 5.0, // 最大缩放比例
            animationMaxScale: 6.0, // 动画最大缩放比例
            initialAlignment: InitialAlignment.center, // 初始对齐方式
          );
        },
        // 在绘制图片后添加蒙版图片
        afterPaintImage:
            (_maskImage != null)
                ? (Canvas canvas, Rect rect, ui.Image image, Paint paint) {
                  if (!rect.isEmpty) {
                    // 获取当前图片的缩放比例
                    final double scaleX = rect.width / image.width;
                    final double scaleY = rect.height / image.height;

                    // 创建一个半透明的画笔，使用传入的不透明度
                    final Paint maskPaint =
                        Paint()
                          ..color = Colors.white.withValues(
                            alpha: widget.maskOpacity,
                          )
                          ..filterQuality = FilterQuality.high
                          ..blendMode = widget.maskBlendMode;

                    // 保存当前画布状态
                    canvas.save();

                    // 将画布移动到图片的左上角
                    canvas.translate(rect.left, rect.top);
                    // 按照图片的缩放比例缩放画布
                    canvas.scale(scaleX, scaleY);

                    // 在画布上绘制蒙版图片
                    canvas.drawImageRect(
                      _maskImage!,
                      Rect.fromLTWH(
                        0,
                        0,
                        _maskImage!.width.toDouble(),
                        _maskImage!.height.toDouble(),
                      ),
                      Rect.fromLTWH(
                        0,
                        0,
                        image.width.toDouble(),
                        image.height.toDouble(),
                      ),
                      maskPaint,
                    );

                    // 恢复画布状态
                    canvas.restore();
                  }
                }
                : null,
      );
    });
  }
}
