import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sficon/flutter_sficon.dart';
import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/hive/setting_box.dart';
import 'package:nanami_flutter/common/utils/logger.dart';

import 'package:smooth_sheets/smooth_sheets.dart';
import 'package:interactive_slider/interactive_slider.dart';

// 函数：显示预览遮罩设置
void showShowMaskSettingSheet(BuildContext context) {
  // 使用 try-catch 包装，确保即使出现异常也能重置状态
  try {
    Navigator.push(
      context,
      ModalSheetRoute(
        barrierColor: Color(0x18000000),
        swipeDismissible: true,
        builder: (context) => const _MenuSheet(),
      ),
    );
  } catch (e) {
    rethrow; // 重新抛出异常，让上层处理
  }
}

// 整体 Sheet
class _MenuSheet extends StatefulWidget {
  const _MenuSheet();

  @override
  State<_MenuSheet> createState() => _MenuSheetState();
}

class _MenuSheetState extends State<_MenuSheet> {
  // 当前选中的值
  late int _selectedValue;

  // 设置 Controller
  final SettingController _settingController = Get.find<SettingController>();

  // logger
  final logger = LoggerUtil.logger;

  @override
  void initState() {
    super.initState();
    // 根据当前混合模式设置初始选中值
    _selectedValue =
        _settingController.showMaskBlendMode.value == BlendMode.multiply
            ? 0
            : 1;
  }

  @override
  Widget build(BuildContext context) {
    return DefaultSheetController(
      child: Sheet(
        shrinkChildToAvoidStaticOverlap: true,
        decoration: SheetDecorationBuilder(
          size: SheetSize.stretch,
          builder: (context, child) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: ColoredBox(
                color: CupertinoColors.systemBackground
                    .resolveFrom(context)
                    .withOpacity(0.5),
                child: child,
              ).asGlass(blurX: 30, blurY: 30),
            );
          },
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题 Bar
            _TopBar(pageTitle: '脸部标注预览', displayUrl: '调整脸部标注区域的显示方式'),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGrey5
                  .resolveFrom(context)
                  .withOpacity(0.5),
            ),
            // 填充
            SizedBox(height: 18),
            // 混合模式设置 - 使用 CupertinoSlidingSegmentedControl 作为开关
            SizedBox(
              width: double.infinity,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 32),
                child: CupertinoSlidingSegmentedControl<int>(
                  groupValue: _selectedValue, // 当前选中状态
                  children: const {
                    0: Padding(
                      padding: EdgeInsets.symmetric(vertical: 9.5),
                      child: Text('遮蔽脸部'),
                    ),
                    1: Padding(
                      padding: EdgeInsets.symmetric(vertical: 9.5),
                      child: Text('遮蔽底图'),
                    ),
                  },
                  onValueChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedValue = value;
                      });
                      // 根据选中值更新混合模式
                      final blendMode =
                          value == 0
                              ? ShowBlendMode.multiply
                              : ShowBlendMode.screen;
                      _settingController.updateShowMaskBlendMode(blendMode);
                    }
                  },
                ),
              ),
            ),

            // 不透明度设置
            Obx(
              () => SizedBox(
                width: double.infinity,
                child: InteractiveSlider(
                  iconPosition: IconPosition.inside,
                  startIcon: Icon(CupertinoIcons.eye_slash),
                  endIcon: Icon(CupertinoIcons.eye),
                  padding: const EdgeInsets.all(16),
                  unfocusedMargin: const EdgeInsets.symmetric(horizontal: 16),
                  centerIcon: Text(
                    '不透明度',
                    style: TextStyle(
                      color: CupertinoColors.systemGrey.resolveFrom(context),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  unfocusedHeight: 40,
                  focusedHeight: 50,
                  iconGap: 16,
                  initialProgress: _settingController.showMaskOpacity.value,
                  onChanged: (value) {
                    // 更新不透明度
                    _settingController.updateShowMaskOpacity(value);
                    logger.d(_settingController.showMaskOpacity.value);
                  },
                  shapeBorder: RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                  ),
                  backgroundColor: CupertinoColors.tertiarySystemFill
                      .resolveFrom(context),
                  foregroundColor: CupertinoDynamicColor.withBrightness(
                    color: Color(0xFFFFFFFF),
                    darkColor: Color(0xFF636366),
                  ).resolveFrom(context),
                  iconColor: CupertinoColors.systemGrey.resolveFrom(context),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 菜单顶部
class _TopBar extends StatelessWidget {
  const _TopBar({required this.pageTitle, required this.displayUrl});

  final String pageTitle;
  final String displayUrl;

  @override
  Widget build(BuildContext context) {
    // 标题
    final pageTitle = Text(
      this.pageTitle,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: Theme.of(context).textTheme.titleMedium,
    );

    // 副标题
    final displayUrl = Text(
      this.displayUrl,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: CupertinoColors.secondaryLabel.resolveFrom(context),
      ),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 左边 Icon
          Container(
            height: 48,
            width: 48,
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground
                  .resolveFrom(context)
                  .withOpacity(0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: CupertinoColors.systemGrey5.resolveFrom(context),
                width: 1.0,
              ),
            ),
            // Icon 内容
            child: Icon(
              CupertinoIcons.eye_fill,
              color: CupertinoColors.label
                  .resolveFrom(context)
                  .withOpacity(0.5),
            ),
          ),
          const SizedBox(width: 16),
          // 中间标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [pageTitle, displayUrl],
            ),
          ),
          const SizedBox(width: 16),
          // 右边关闭按钮
          const _CloseButton(),
        ],
      ),
    );
  }
}

// 菜单顶部关闭按钮
class _CloseButton extends StatelessWidget {
  const _CloseButton();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // 然后关闭弹出框
        Navigator.pop(context);
      },
      child: Container(
        width: 36,
        height: 36,
        decoration: ShapeDecoration(
          shape: CircleBorder(),
          color: CupertinoColors.systemGrey5
              .resolveFrom(context)
              .withOpacity(0.7),
        ),
        child: Center(
          child: SFIcon(
            SFIcons.sf_xmark,
            fontSize: 18,
            fontWeight: FontWeight.w900,
            color: CupertinoColors.label.resolveFrom(context).withOpacity(0.5),
          ),
        ),
      ),
    );
  }
}
