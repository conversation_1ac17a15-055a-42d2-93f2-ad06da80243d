import 'package:flutter/material.dart';

class BottomActions extends StatelessWidget {
  final List<Widget> actions; // 直接接收 Widget 列表
  final Color backgroundColor; // 容器背景色
  final double? verticalPadding; // 垂直内边距
  final double? horizontalPadding; // 水平内边距

  const BottomActions({
    super.key,
    required this.actions,
    this.backgroundColor = Colors.transparent, // 参考 GithubIssues 的 AppBar 背景色
    this.verticalPadding,
    this.horizontalPadding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor, // 整个组件的背景色
      padding: EdgeInsets.symmetric(
        vertical: verticalPadding ?? 8.0,
      ), // 使用传入的垂直内边距或默认值
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding ?? 15.0,
          ), // 使用传入的水平内边距或默认值
          child: Row(
            children:
                actions.map((action) {
                  // 为每个 action 创建间距
                  return Padding(
                    padding: const EdgeInsets.only(right: 10.0), // 每个按钮右侧间距
                    child: action,
                  );
                }).toList(),
          ),
        ),
      ),
    );
  }
}
