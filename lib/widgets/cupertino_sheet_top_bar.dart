import 'package:flutter/cupertino.dart';

class CupertinoSheetTopBar extends StatelessWidget
    implements PreferredSizeWidget {
  const CupertinoSheetTopBar({
    super.key,
    required this.title,
    this.leading,
    this.trailing,
    this.backgroundColor,
  });

  final Widget title;
  final Widget? leading;
  final Widget? trailing;
  final Color? backgroundColor;

  @override
  Size get preferredSize => const Size.fromHeight(56);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: preferredSize.height,
      decoration: BoxDecoration(
        color:
            backgroundColor ?? CupertinoColors.systemGrey6.resolveFrom(context),
        // border: Border(bottom: BorderSide(color: CupertinoColors.systemGrey5)),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (leading case final leading?) Positioned(left: 1, child: leading),
          DefaultTextStyle(
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: CupertinoColors.label.resolveFrom(context),
            ),
            child: title,
          ),
          if (trailing case final trailing?)
            Positioned(
              right: 1,
              child: DefaultTextStyle(
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                child: trailing,
              ),
            ),
        ],
      ),
    );
  }
}

class SiteIcon extends StatelessWidget {
  const SiteIcon({super.key, required this.url});

  final String url;

  @override
  Widget build(BuildContext context) {
    return SizedBox.square(dimension: 48, child: Image.network(url));
  }
}
