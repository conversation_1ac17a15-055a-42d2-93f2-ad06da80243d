import 'dart:ui';
import 'package:flutter/cupertino.dart';

/// 动态磨砂玻璃效果组件
///
/// 可以根据当前主题自动调整背景颜色
///
/// [child] - 要显示在磨砂玻璃上的子组件
/// [blur] - 模糊程度，值越大越模糊，默认10.0
/// [opacity] - 背景颜色的不透明度，默认0.5
/// [lightColor] - 亮色模式下的背景颜色，默认白色
/// [darkColor] - 暗色模式下的背景颜色，默认黑色
/// [borderRadius] - 边框圆角
class FrostedGlass extends StatelessWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final Color lightColor;
  final Color darkColor;
  final BorderRadius? borderRadius;

  const FrostedGlass(
    Key? key, {
    required this.child,
    this.blur = 40.0,
    this.opacity = 0.4,
    this.lightColor = CupertinoColors.white,
    this.darkColor = CupertinoColors.black,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取当前亮度模式
    final brightness = MediaQuery.platformBrightnessOf(context);
    final isDarkMode = brightness == Brightness.dark;

    // 根据亮度模式选择颜色
    final color = isDarkMode ? darkColor : lightColor;

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            color: color.withOpacity(opacity),
            borderRadius: borderRadius,
          ),
          child: child,
        ),
      ),
    );
  }
}
