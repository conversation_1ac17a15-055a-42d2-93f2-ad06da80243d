import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_sficon/flutter_sficon.dart';
import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/utils/logger.dart';

import 'package:smooth_sheets/smooth_sheets.dart';

// 函数：显示人脸检测设置
void showFaceDetectSettingSheet(BuildContext context) {
  // 使用 try-catch 包装，确保即使出现异常也能重置状态
  try {
    Navigator.push(
      context,
      ModalSheetRoute(
        barrierColor: Color(0x18000000),
        swipeDismissible: true,
        builder: (context) => const _MenuSheet(),
      ),
    );
  } catch (e) {
    rethrow; // 重新抛出异常，让上层处理
  }
}

// 整体 Sheet
class _MenuSheet extends StatefulWidget {
  const _MenuSheet();

  @override
  State<_MenuSheet> createState() => _MenuSheetState();
}

class _MenuSheetState extends State<_MenuSheet> {
  // 设置 Controller
  final SettingController _settingController = Get.find<SettingController>();

  // logger
  final logger = LoggerUtil.logger;

  @override
  Widget build(BuildContext context) {
    return DefaultSheetController(
      child: Sheet(
        shrinkChildToAvoidStaticOverlap: true,
        decoration: SheetDecorationBuilder(
          size: SheetSize.stretch,
          builder: (context, child) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: ColoredBox(
                color: CupertinoColors.systemBackground
                    .resolveFrom(context)
                    .withOpacity(0.5),
                child: child,
              ).asGlass(blurX: 30, blurY: 30),
            );
          },
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题 Bar
            _TopBar(pageTitle: '人脸检测设置', displayUrl: '调整人脸检测区域的范围扩展倍率'),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGrey5
                  .resolveFrom(context)
                  .withOpacity(0.5),
            ),

            // 填充
            SizedBox(height: 18),
            // 范围扩展倍率设置
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 26.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和当前值
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '范围扩展倍率',
                        style: TextStyle(
                          color: CupertinoColors.label.resolveFrom(context),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Obx(
                        () => Text(
                          '${_settingController.faceDetectExpansionRate.value}%',
                          style: TextStyle(
                            color: CupertinoColors.secondaryLabel.resolveFrom(
                              context,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  // 滑块
                  SizedBox(
                    width: double.infinity, // 让滑块铺满整个宽度
                    child: Obx(
                      () => CupertinoSlider(
                        value:
                            _settingController.faceDetectExpansionRate.value
                                .toDouble(),
                        min: -25,
                        max: 25,
                        onChanged: (value) {
                          // 更新扩展倍率
                          _settingController.updateFaceDetectExpansionRate(
                            value.toInt(),
                          );
                          logger.d(
                            '人脸检测扩展倍率: ${_settingController.faceDetectExpansionRate.value}',
                          );
                        },
                      ),
                    ),
                  ),
                  // 说明文字
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '缩小',
                          style: TextStyle(
                            fontSize: 12,
                            color: CupertinoColors.secondaryLabel.resolveFrom(
                              context,
                            ),
                          ),
                        ),
                        Text(
                          '扩大',
                          style: TextStyle(
                            fontSize: 12,
                            color: CupertinoColors.secondaryLabel.resolveFrom(
                              context,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 菜单顶部
class _TopBar extends StatelessWidget {
  const _TopBar({required this.pageTitle, required this.displayUrl});

  final String pageTitle;
  final String displayUrl;

  @override
  Widget build(BuildContext context) {
    // 标题
    final pageTitle = Text(
      this.pageTitle,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: Theme.of(context).textTheme.titleMedium,
    );

    // 副标题
    final displayUrl = Text(
      this.displayUrl,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: CupertinoColors.secondaryLabel.resolveFrom(context),
      ),
    );

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左边 Icon
          Container(
            height: 48,
            width: 48,
            decoration: BoxDecoration(
              color: CupertinoColors.systemBackground
                  .resolveFrom(context)
                  .withOpacity(0.3),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: CupertinoColors.systemGrey5.resolveFrom(context),
                width: 1.0,
              ),
            ),
            // Icon 内容
            child: Icon(
              CupertinoIcons.person_crop_circle,
              color: CupertinoColors.label
                  .resolveFrom(context)
                  .withOpacity(0.5),
            ),
          ),
          const SizedBox(width: 16),
          // 中间标题
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [pageTitle, displayUrl],
            ),
          ),
          const SizedBox(width: 16),
          // 右边关闭按钮
          const _CloseButton(),
        ],
      ),
    );
  }
}

// 菜单顶部关闭按钮
class _CloseButton extends StatelessWidget {
  const _CloseButton();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // 然后关闭弹出框
        Navigator.pop(context);
      },
      child: Container(
        width: 36,
        height: 36,
        decoration: ShapeDecoration(
          shape: CircleBorder(),
          color: CupertinoColors.systemGrey5
              .resolveFrom(context)
              .withOpacity(0.7),
        ),
        child: Center(
          child: SFIcon(
            SFIcons.sf_xmark,
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: CupertinoColors.label.resolveFrom(context).withOpacity(0.5),
          ),
        ),
      ),
    );
  }
}
