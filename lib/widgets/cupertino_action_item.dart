import 'package:flutter/cupertino.dart';

/// iOS风格的操作按钮组件
///
/// 该组件遵循iOS原生Cupertino设计风格，使用CupertinoColors系统颜色
/// 不包含徽章功能，无边框，颜色自动适应系统主题
class CupertinoActionItem extends StatelessWidget {
  /// 按钮图标
  final IconData? icon;

  /// 按钮文本
  final String? text;

  /// 点击回调
  final VoidCallback? onPressed;

  /// 是否显示下拉箭头
  final bool showDropdownArrow;

  /// 文本大小
  final double textSize;

  /// 图标大小
  final double iconSize;

  // 是否危险
  final bool isDestructive;

  const CupertinoActionItem({
    super.key,
    this.icon,
    this.text,
    this.onPressed,
    this.showDropdownArrow = false,
    this.textSize = 15.0,
    this.iconSize = 15.0,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      onPressed: onPressed,
      padding: EdgeInsets.zero, // 移除默认内边距
      minSize: 0, // 移除最小尺寸限制
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 5.0),
        decoration: BoxDecoration(
          color: CupertinoColors.secondarySystemBackground.resolveFrom(context),
          borderRadius: BorderRadius.circular(50.0), // 圆角
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min, // 让 Row 包裹内容
          crossAxisAlignment: CrossAxisAlignment.center, // 确保垂直居中
          children: [
            // 显示图标（如果提供）
            if (icon != null)
              Icon(
                icon,
                color:
                    isDestructive
                        ? CupertinoColors.systemRed.resolveFrom(context)
                        : CupertinoColors.activeBlue.resolveFrom(context),
                size: iconSize,
              ),

            // 图标和文本之间的间距
            if (icon != null && text != null) const SizedBox(width: 4),

            // 显示文本（如果提供）
            if (text != null)
              Text(
                text!,
                style: TextStyle(
                  fontSize: textSize,
                  color:
                      isDestructive
                          ? CupertinoColors.systemRed.resolveFrom(context)
                          : CupertinoColors.activeBlue.resolveFrom(context),
                ),
              ),

            // 显示下拉箭头（如果需要）
            if (showDropdownArrow)
              Padding(
                padding: const EdgeInsets.only(left: 2.0), // 箭头与左侧元素的间距
                child: Icon(
                  CupertinoIcons.chevron_down,
                  // color: CupertinoColors.tertiaryLabel.resolveFrom(context),
                  color:
                      isDestructive
                          ? CupertinoColors.systemRed.resolveFrom(context)
                          : CupertinoColors.activeBlue.resolveFrom(context),
                  size: iconSize,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
