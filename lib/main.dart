import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/binding/app_binding.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/hive/init.dart';
import 'package:nanami_flutter/common/services/database_service.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/common/utils/path.dart';
import 'package:nanami_flutter/common/utils/permission_util.dart';

import 'package:nanami_flutter/pages/welcome_page.dart';
import 'package:toastification/toastification.dart';

void main() async {
  // 确保 Flutter 绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  // FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  // 初始化目录获取
  await PathUtils.init();

  LoggerUtil.logger.d('输出目录: ${PathUtils.overlayOutputDir}');

  // 初始化 Hive
  await initHive(PathUtils.hiveDir);

  // 初始化数据库服务
  await Get.putAsync(() => DatabaseService().init());

  // 运行应用
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  // 初始化 Controllers
  final settingController = Get.put(SettingController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // 使用 SettingController 中的方法获取当前亮度
      final currentBrightness = settingController.getCurrentBrightness(context);

      LoggerUtil.logger.d('当前亮度: $currentBrightness');

      SystemUiOverlayStyle systemUiOverlayStyle = SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.transparent, //导航栏颜色
        systemNavigationBarIconBrightness:
            currentBrightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark, //导航栏图标颜色
        systemNavigationBarDividerColor: Colors.transparent, //系统导航栏分隔线颜色
        systemNavigationBarContrastEnforced: false, //系统导航栏对比度强制
      );

      return AnnotatedRegion<SystemUiOverlayStyle>(
        value: systemUiOverlayStyle,
        child: ToastificationWrapper(
          child: GetCupertinoApp(
            title: "七海贴图",
            theme: CupertinoThemeData(
              primaryColor: CupertinoColors.activeBlue, // iOS 蓝色
              // primaryColor: MyPrimaryColor.pink, // 自定义颜色
              brightness: currentBrightness,
            ),
            initialBinding: AppBinding(),
            defaultTransition: Transition.cupertino,
            // transitionDuration: const Duration(milliseconds: 600),
            debugShowCheckedModeBanner: false, // 关闭debug显示标志
            home: const WelcomePage(),
            // 添加本地化支持
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: [
              const Locale('zh', 'CN'), // 中文
              const Locale('en', 'US'), // 英文
            ],
          ),
        ),
      );
    });
  }
}
