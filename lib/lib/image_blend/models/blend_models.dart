/// 图像混合相关的数据模型和枚举定义

/// 混合模式枚举
enum BlendMode {
  /// 正常模式
  normal(0),

  /// 正片叠底
  multiply(1),

  /// 叠加
  overlay(2),

  /// 柔光
  softLight(3),

  /// 滤色
  screen(4);

  const BlendMode(this.value);
  final int value;
}

/// 平铺模式枚举
enum TilingMode {
  /// 拉伸
  stretch(0),

  /// 平铺
  tile(1);

  const TilingMode(this.value);
  final int value;
}

/// FFI 错误码
class FFIErrorCodes {
  static const int success = 0;
  static const int invalidParams = -1;
  static const int fileNotFound = -2;
  static const int processingFailed = -3;
  static const int outOfMemory = -4;
}

/// 图像叠加参数
class OverlayImageParams {
  /// 底图路径
  final String baseImagePath;

  /// 顶图路径
  final String topImagePath;

  /// 混合模式
  final BlendMode blendMode;

  /// 平铺模式
  final TilingMode tilingMode;

  /// 透明度 (0-100)
  final int opacity;

  /// 输出路径
  final String outputPath;

  /// 平铺缩放比例 (1-1000)
  final int tilingScale;

  /// 蒙版图像路径（可选）
  final String? maskImagePath;

  const OverlayImageParams({
    required this.baseImagePath,
    required this.topImagePath,
    this.blendMode = BlendMode.normal,
    this.tilingMode = TilingMode.stretch,
    this.opacity = 100,
    required this.outputPath,
    this.tilingScale = 100,
    this.maskImagePath,
  });

  /// 验证参数有效性
  bool isValid() {
    return baseImagePath.isNotEmpty &&
        topImagePath.isNotEmpty &&
        outputPath.isNotEmpty &&
        opacity >= 0 &&
        opacity <= 100 &&
        tilingScale >= 1 &&
        tilingScale <= 1000;
  }
}

/// 图像平铺参数
class TileImageParams {
  /// 图像路径
  final String imagePath;

  /// 平铺倍数 (1-10)
  final int tileMultiplier;

  /// 输出路径
  final String outputPath;

  const TileImageParams({
    required this.imagePath,
    required this.tileMultiplier,
    required this.outputPath,
  });

  /// 验证参数有效性
  bool isValid() {
    return imagePath.isNotEmpty &&
        outputPath.isNotEmpty &&
        tileMultiplier >= 1 &&
        tileMultiplier <= 10;
  }
}

/// 图像裁剪参数
class TrimImageParams {
  /// 输入图像路径
  final String inputPath;

  /// 输出路径
  final String outputPath;

  const TrimImageParams({required this.inputPath, required this.outputPath});

  /// 验证参数有效性
  bool isValid() {
    return inputPath.isNotEmpty && outputPath.isNotEmpty;
  }
}

/// FFI 操作结果
class FFIResult {
  /// 错误码
  final int errorCode;

  /// 是否成功
  final bool isSuccess;

  /// 错误消息
  final String? errorMessage;

  const FFIResult({required this.errorCode, this.errorMessage})
    : isSuccess = errorCode == FFIErrorCodes.success;

  /// 创建成功结果
  factory FFIResult.success() {
    return const FFIResult(errorCode: FFIErrorCodes.success);
  }

  /// 创建错误结果
  factory FFIResult.error(int errorCode, [String? message]) {
    return FFIResult(
      errorCode: errorCode,
      errorMessage: message ?? _getErrorMessage(errorCode),
    );
  }

  /// 根据错误码获取错误消息
  static String _getErrorMessage(int errorCode) {
    switch (errorCode) {
      case FFIErrorCodes.invalidParams:
        return '参数无效';
      case FFIErrorCodes.fileNotFound:
        return '文件未找到';
      case FFIErrorCodes.processingFailed:
        return '处理失败';
      case FFIErrorCodes.outOfMemory:
        return '内存不足';
      default:
        return '未知错误 ($errorCode)';
    }
  }

  @override
  String toString() {
    if (isSuccess) {
      return 'FFIResult(success)';
    } else {
      return 'FFIResult(error: $errorCode, message: $errorMessage)';
    }
  }
}
