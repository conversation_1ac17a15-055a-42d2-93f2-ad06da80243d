import 'dart:ffi';
import 'dart:io';
import 'dart:isolate';
import 'package:ffi/ffi.dart';

import 'models/blend_models.dart';

/// FFI 函数签名定义

/// overlay_images 函数签名
typedef OverlayImagesNative =
    Int32 Function(
      Pointer<Utf8> baseImagePath,
      Pointer<Utf8> topImagePath,
      Int32 blendMode,
      Int32 tilingMode,
      Int32 opacity,
      Pointer<Utf8> outputPath,
      Int32 tilingScale,
      Pointer<Utf8> maskImagePath,
    );

typedef OverlayImagesDart =
    int Function(
      Pointer<Utf8> baseImagePath,
      Pointer<Utf8> topImagePath,
      int blendMode,
      int tilingMode,
      int opacity,
      Pointer<Utf8> outputPath,
      int tilingScale,
      Pointer<Utf8> maskImagePath,
    );

/// tile_image 函数签名
typedef TileImageNative =
    Int32 Function(
      Pointer<Utf8> imagePath,
      Int32 tileMultiplier,
      Pointer<Utf8> outputPath,
    );

typedef TileImageDart =
    int Function(
      Pointer<Utf8> imagePath,
      int tileMultiplier,
      Pointer<Utf8> outputPath,
    );

/// trim_image 函数签名
typedef TrimImageNative =
    Int32 Function(Pointer<Utf8> inputPath, Pointer<Utf8> outputPath);

typedef TrimImageDart =
    int Function(Pointer<Utf8> inputPath, Pointer<Utf8> outputPath);

/// clear_cache 函数签名
typedef ClearCacheNative = Int32 Function();
typedef ClearCacheDart = int Function();

/// 图像混合 FFI 绑定类
class ImageBlendFFI {
  static ImageBlendFFI? _instance;
  late final DynamicLibrary _library;
  late final OverlayImagesDart _overlayImages;
  late final TileImageDart _tileImage;
  late final TrimImageDart _trimImage;
  late final ClearCacheDart _clearCache;

  /// 单例模式
  factory ImageBlendFFI() {
    return _instance ??= ImageBlendFFI._internal();
  }

  ImageBlendFFI._internal() {
    _loadLibrary();
    _bindFunctions();
  }

  /// 加载动态库
  void _loadLibrary() {
    if (Platform.isAndroid) {
      _library = DynamicLibrary.open('libimage_blend_ffi.so');
    } else if (Platform.isIOS) {
      _library = DynamicLibrary.process();
    } else {
      throw UnsupportedError('不支持的平台: ${Platform.operatingSystem}');
    }
  }

  /// 绑定 FFI 函数
  void _bindFunctions() {
    _overlayImages =
        _library
            .lookup<NativeFunction<OverlayImagesNative>>('overlay_images')
            .asFunction();

    _tileImage =
        _library
            .lookup<NativeFunction<TileImageNative>>('tile_image')
            .asFunction();

    _trimImage =
        _library
            .lookup<NativeFunction<TrimImageNative>>('trim_image')
            .asFunction();

    _clearCache =
        _library
            .lookup<NativeFunction<ClearCacheNative>>('clear_cache')
            .asFunction();
  }

  /// 同步调用图像叠加函数
  int overlayImagesSync(OverlayImageParams params) {
    final basePathPtr = params.baseImagePath.toNativeUtf8();
    final topPathPtr = params.topImagePath.toNativeUtf8();
    final outputPathPtr = params.outputPath.toNativeUtf8();
    final maskPathPtr = params.maskImagePath?.toNativeUtf8() ?? nullptr;

    try {
      return _overlayImages(
        basePathPtr,
        topPathPtr,
        params.blendMode.value,
        params.tilingMode.value,
        params.opacity,
        outputPathPtr,
        params.tilingScale,
        maskPathPtr,
      );
    } finally {
      malloc.free(basePathPtr);
      malloc.free(topPathPtr);
      malloc.free(outputPathPtr);
      if (maskPathPtr != nullptr) {
        malloc.free(maskPathPtr);
      }
    }
  }

  /// 同步调用图像平铺函数
  int tileImageSync(TileImageParams params) {
    final imagePathPtr = params.imagePath.toNativeUtf8();
    final outputPathPtr = params.outputPath.toNativeUtf8();

    try {
      return _tileImage(imagePathPtr, params.tileMultiplier, outputPathPtr);
    } finally {
      malloc.free(imagePathPtr);
      malloc.free(outputPathPtr);
    }
  }

  /// 同步调用图像裁剪函数
  int trimImageSync(TrimImageParams params) {
    final inputPathPtr = params.inputPath.toNativeUtf8();
    final outputPathPtr = params.outputPath.toNativeUtf8();

    try {
      return _trimImage(inputPathPtr, outputPathPtr);
    } finally {
      malloc.free(inputPathPtr);
      malloc.free(outputPathPtr);
    }
  }

  /// 同步调用清理缓存函数
  int clearCacheSync() {
    return _clearCache();
  }
}

/// Isolate 中执行的函数

/// 在 Isolate 中执行图像叠加
void _overlayImagesIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message is OverlayImageParams) {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.overlayImagesSync(message);
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(
          FFIResult.error(FFIErrorCodes.processingFailed, e.toString()),
        );
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// 在 Isolate 中执行图像平铺
void _tileImageIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message is TileImageParams) {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.tileImageSync(message);
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(
          FFIResult.error(FFIErrorCodes.processingFailed, e.toString()),
        );
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// 在 Isolate 中执行图像裁剪
void _trimImageIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message is TrimImageParams) {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.trimImageSync(message);
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(
          FFIResult.error(FFIErrorCodes.processingFailed, e.toString()),
        );
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// 在 Isolate 中执行清理缓存
void _clearCacheIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message == 'clear') {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.clearCacheSync();
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(
          FFIResult.error(FFIErrorCodes.processingFailed, e.toString()),
        );
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// 异步 FFI 操作辅助类
class ImageBlendAsyncHelper {
  /// 使用 Isolate 执行图像叠加操作
  static Future<FFIResult> overlayImagesAsync(OverlayImageParams params) async {
    if (!params.isValid()) {
      return FFIResult.error(FFIErrorCodes.invalidParams, '参数验证失败');
    }

    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(
      _overlayImagesIsolate,
      receivePort.sendPort,
    );

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send(params);
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }

  /// 使用 Isolate 执行图像平铺操作
  static Future<FFIResult> tileImageAsync(TileImageParams params) async {
    if (!params.isValid()) {
      return FFIResult.error(FFIErrorCodes.invalidParams, '参数验证失败');
    }

    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(
      _tileImageIsolate,
      receivePort.sendPort,
    );

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send(params);
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }

  /// 使用 Isolate 执行图像裁剪操作
  static Future<FFIResult> trimImageAsync(TrimImageParams params) async {
    if (!params.isValid()) {
      return FFIResult.error(FFIErrorCodes.invalidParams, '参数验证失败');
    }

    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(
      _trimImageIsolate,
      receivePort.sendPort,
    );

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send(params);
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }

  /// 使用 Isolate 执行清理缓存操作
  static Future<FFIResult> clearCacheAsync() async {
    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(
      _clearCacheIsolate,
      receivePort.sendPort,
    );

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send('clear');
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }
}
