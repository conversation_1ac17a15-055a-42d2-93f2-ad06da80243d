import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:ui';
import 'package:nanami_flutter/common/controllers/main_navigation_controller.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/sheets/select_image_group_sheet.dart';
import 'package:nanami_flutter/pages/tab_pages/overlay/image_overlay_page.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/my_image_groups_page.dart';
import 'package:nanami_flutter/pages/tab_pages/image/my_images_page.dart';
import 'package:nanami_flutter/pages/tab_pages/setting/setting_page.dart';

/// 主导航页面
class MainNavigation extends GetView<MainNavigationController> {
  const MainNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    // 定义页面列表
    final List<Widget> pages = [
      const MyImagesPage(),
      const MyImageGroupsPage(),
      ImageOverlayPage(),
      const SettingPage(),
    ];

    return Obx(() {
      // 如果在多选模式下，使用自定义的底部操作栏
      if (controller.showMultiSelectBar.value) {
        return _buildMultiSelectScaffold(context, pages);
      }

      // 否则使用正常的 TabBar
      return CupertinoTabScaffold(
        resizeToAvoidBottomInset: false, // 防止键盘弹出时底部导航栏被顶起
        tabBar: _buildNormalTabBar(context),
        tabBuilder: (context, index) {
          return pages[index];
        },
      );
    });
  }

  // 构建普通的底部导航栏
  CupertinoTabBar _buildNormalTabBar(BuildContext context) {
    return CupertinoTabBar(
      backgroundColor: MyBgColor.tabBarBgColor(context),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(CupertinoIcons.photo_fill_on_rectangle_fill, size: 24),
          label: '图库',
        ),
        BottomNavigationBarItem(
          icon: Icon(CupertinoIcons.collections_solid, size: 24),
          label: '相簿',
        ),
        BottomNavigationBarItem(
          icon: Icon(CupertinoIcons.layers, size: 25),
          label: '贴膜',
        ),
        BottomNavigationBarItem(
          icon: Icon(CupertinoIcons.settings, size: 24),
          label: '设置',
        ),
      ],
      currentIndex: controller.currentIndex.value,
      onTap: controller.changeTab,
    );
  }

  // 构建多选模式下的 Scaffold
  Widget _buildMultiSelectScaffold(BuildContext context, List<Widget> pages) {
    final myImagesController = controller.myImagesController;
    if (myImagesController == null) {
      // 如果无法获取图片控制器，回退到普通模式
      return CupertinoTabScaffold(
        resizeToAvoidBottomInset: false,
        tabBar: _buildNormalTabBar(context),
        tabBuilder: (context, index) => pages[index],
      );
    }

    // 计算底部栏高度
    final bottomBarHeight = 50.0 + MediaQuery.of(context).padding.bottom;

    // 使用 Stack 构建带有悬浮底部操作栏的页面
    return Scaffold(
      body: Stack(
        children: [
          // 底层显示图片页面 - 使用 MediaQuery 修改底部 padding
          MediaQuery(
            data: MediaQuery.of(context).copyWith(
              padding: MediaQuery.of(
                context,
              ).padding.copyWith(bottom: bottomBarHeight),
            ),
            child: SizedBox(
              height: MediaQuery.of(context).size.height,
              child: pages[0],
            ),
          ),

          // 悬浮在底部的操作栏
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                child: Container(
                  height: bottomBarHeight,
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).padding.bottom,
                  ),
                  decoration: BoxDecoration(
                    color: MyBgColor.tabBarBgColor(
                      context,
                    ).resolveFrom(context),
                    border: Border(
                      top: BorderSide(
                        color: CupertinoColors.systemGrey5.resolveFrom(context),
                        width: 0.5,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: CupertinoColors.systemGrey5
                            .resolveFrom(context)
                            .withOpacity(0.3),
                        blurRadius: 5,
                        offset: const Offset(0, -1),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 左侧添加到相簿
                      Obx(
                        () => CupertinoButton(
                          padding: const EdgeInsets.only(left: 16),
                          onPressed:
                              myImagesController.selectedImageIds.isEmpty
                                  ? null
                                  : () async {
                                    // 获取选中的图片ID列表
                                    final selectedIds =
                                        myImagesController.selectedImageIds;
                                    // 弹出选择相簿的 Sheet
                                    int? groupId =
                                        await showSelectImageGroupSheet(
                                          context,
                                          title: "添加到相簿",
                                        );
                                    if (groupId == null) return;
                                    // 将图片添加到相簿
                                    for (var id in selectedIds) {
                                      await ImageGroupService.addImageToGroup(
                                        groupId,
                                        id,
                                      );
                                    }
                                    // 退出多选模式
                                    myImagesController.exitMultiSelectMode();
                                  },
                          child: const Icon(
                            CupertinoIcons.rectangle_stack_badge_plus,
                            size: 24,
                          ),
                        ),
                      ),
                      // 中间显示已选择的图片数量
                      Obx(
                        () => Text(
                          myImagesController.selectedImageIds.isEmpty
                              ? "选择项目"
                              : "已选择 ${myImagesController.selectController.value.amount} 张图片",
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // 右侧删除按钮
                      Obx(
                        () => CupertinoButton(
                          padding: const EdgeInsets.only(right: 16),

                          onPressed:
                              myImagesController.selectedImageIds.isEmpty
                                  ? null
                                  : () {
                                    // 删除功能
                                    // 获取选中的图片ID列表
                                    final selectedIds =
                                        myImagesController.selectedImageIds;
                                    if (selectedIds.isEmpty) return;
                                    // 显示确认对话框
                                    showCupertinoDialog(
                                      context: context,
                                      builder:
                                          (context) => CupertinoAlertDialog(
                                            title: const Text('要删除选择的图片吗？'),
                                            content: Text(
                                              '将会在 “七海” 中删除选中的 ${selectedIds.length} 张图片。',
                                            ),
                                            actions: [
                                              CupertinoDialogAction(
                                                child: const Text('取消'),
                                                onPressed:
                                                    () =>
                                                        Navigator.pop(context),
                                              ),
                                              CupertinoDialogAction(
                                                isDestructiveAction: true,
                                                child: const Text('删除'),
                                                onPressed: () async {
                                                  // 弹出对话框
                                                  Navigator.pop(context);

                                                  // 获取选中的图片ID列表
                                                  final selectedIds =
                                                      myImagesController
                                                          .selectedImageIds
                                                          .toList();

                                                  // // 显示加载指示器
                                                  // myImagesController.isLoading.value =
                                                  //     true;

                                                  // 循环删除所有选中的图片
                                                  for (var imageId
                                                      in selectedIds) {
                                                    await myImagesController
                                                        .deleteImage(imageId);
                                                  }

                                                  // 退出多选模式
                                                  myImagesController
                                                      .exitMultiSelectMode();
                                                },
                                              ),
                                            ],
                                          ),
                                    );
                                  },
                          child: const Icon(
                            CupertinoIcons.delete,
                            size: 24,
                            // color: CupertinoColors.destructiveRed,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
