import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/services/notification_service.dart';
import 'package:nanami_flutter/common/utils/logger.dart';

class NotificationTestPage extends StatelessWidget {
  const NotificationTestPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(middle: Text('通知测试')),
      child: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('点击下面的按钮测试发送通知', style: TextStyle(fontSize: 16)),
              const SizedBox(height: 20),
              CupertinoButton.filled(
                child: const Text('发送测试通知'),
                onPressed: () async {
                  try {
                    LoggerUtil.logger.d('开始发送测试通知');
                    await NotificationService.to.testNotification();
                    LoggerUtil.logger.d('测试通知发送调用完成');

                    // 在iOS上显示提示
                    if (Platform.isIOS) {
                      showCupertinoDialog(
                        context: context,
                        builder:
                            (context) => CupertinoAlertDialog(
                              title: const Text('通知已发送'),
                              content: const Text('如果你看不到通知，请检查通知权限设置'),
                              actions: [
                                CupertinoDialogAction(
                                  child: const Text('确定'),
                                  onPressed: () => Navigator.of(context).pop(),
                                ),
                              ],
                            ),
                      );
                    }
                  } catch (e) {
                    LoggerUtil.logger.e('发送通知时出错', error: e);

                    showCupertinoDialog(
                      context: context,
                      builder:
                          (context) => CupertinoAlertDialog(
                            title: const Text('发送通知失败'),
                            content: Text('错误信息: $e'),
                            actions: [
                              CupertinoDialogAction(
                                child: const Text('确定'),
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                            ],
                          ),
                    );
                  }
                },
              ),
              const SizedBox(height: 20),
              CupertinoButton(
                child: const Text('检查iOS通知权限'),
                onPressed: () async {
                  if (Platform.isIOS) {
                    final bool hasPermission =
                        await NotificationService.to
                            .checkIOSNotificationPermissionStatus();

                    showCupertinoDialog(
                      context: context,
                      builder:
                          (context) => CupertinoAlertDialog(
                            title: const Text('iOS通知权限状态'),
                            content: Text(
                              hasPermission ? '已获取通知权限' : '未获取通知权限',
                            ),
                            actions: [
                              CupertinoDialogAction(
                                child: const Text('确定'),
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                            ],
                          ),
                    );
                  } else {
                    showCupertinoDialog(
                      context: context,
                      builder:
                          (context) => CupertinoAlertDialog(
                            title: const Text('不适用'),
                            content: const Text('此功能只在iOS上可用'),
                            actions: [
                              CupertinoDialogAction(
                                child: const Text('确定'),
                                onPressed: () => Navigator.of(context).pop(),
                              ),
                            ],
                          ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
