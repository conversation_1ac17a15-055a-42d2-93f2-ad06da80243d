import 'package:flutter/cupertino.dart';
import 'package:cupertino_onboarding/cupertino_onboarding.dart';
import 'package:nanami_flutter/common/theme/text.dart';
import 'package:nanami_flutter/pages/login_page.dart';

class WelcomePage extends StatelessWidget {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoOnboarding(
      // 跳转到登录页
      onPressedOnLastPage:
          () => Navigator.pushReplacement(
            context,
            CupertinoPageRoute(builder: (context) => const LoginPage()),
          ),
      bottomButtonChild: const Text('继续'),
      pages: [
        WhatsNewPage(
          title: const Text("欢迎使用七海"),
          features: [
            WhatsNewFeature(
              icon: Icon(CupertinoIcons.photo_on_rectangle),
              title: const Text('图片管理'),
              description: const Text('轻松导入照片，并且随您的喜好进行命名、分组。让一切都井然有序。'),
            ),
            WhatsNewFeature(
              icon: Icon(CupertinoIcons.layers),
              title: const Text('图片贴膜'),
              description: const Text("迄今为止速度最快的图片贴膜应用，配合高精度的脸部识别完成自动擦脸。"),
            ),
            WhatsNewFeature(
              icon: Icon(CupertinoIcons.share),
              title: const Text('快速分享'),
              description: const Text("仅需轻触一键，将处理好的图片发送到 QQ 。从未如此流畅。"),
            ),
          ],
        ),
        CupertinoOnboardingPage(
          title: const Text('本地处理'),
          bodyPadding: EdgeInsets.symmetric(horizontal: 30.0),
          body: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(CupertinoIcons.square_stack_3d_down_right, size: 200),
              const SizedBox(height: 30),
              Text(
                '您的图片始终由您掌控。所有图片处理均在您的设备本地进行，不上传至云端。图片始终安全私密，更能节省时间与流量。',
                style: MyTextStyle.descriptionTextStyle(context),
              ),
            ],
          ),
        ),
        CupertinoOnboardingPage(
          title: const Text('极致性能'),
          bodyPadding: EdgeInsets.symmetric(horizontal: 30.0),
          body: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(CupertinoIcons.rocket, size: 200),
                  const SizedBox(height: 30),
                  RichText(
                    text: TextSpan(
                      style: MyTextStyle.descriptionTextStyle(context),
                      children: [
                        TextSpan(
                          text:
                              '感受由创新技术驱动的疾速性能，处理速度实现惊人跃升。贴膜速度相比同类应用最高可快达 30 倍',
                        ),
                        WidgetSpan(
                          child: Transform.translate(
                            offset: const Offset(0, -7),
                            child: Text(
                              ' ¹',
                              style: TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: CupertinoColors.secondaryLabel
                                    .resolveFrom(context),
                              ),
                            ),
                          ),
                        ),
                        TextSpan(text: '，让批量任务如闪电般转瞬完成。'),
                      ],
                    ),
                  ),
                ],
              ),
              Positioned(
                bottom: 10,
                left: 0,
                right: 0,
                child: Text(
                  '¹ 数据来源于七海实验室，基于统一测试条件进行对比测试。',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 11,
                    fontStyle: FontStyle.italic,
                    color: CupertinoColors.tertiaryLabel.resolveFrom(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
