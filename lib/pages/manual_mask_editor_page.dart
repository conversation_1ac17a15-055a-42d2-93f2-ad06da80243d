import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_drawing_board/flutter_drawing_board.dart';
import 'package:flutter_drawing_board/paint_contents.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'package:image_size_getter/file_input.dart';
import 'package:image_size_getter/image_size_getter.dart';
import 'package:nanami_flutter/common/controllers/add_image_controller.dart';
import 'package:nanami_flutter/common/utils/human_face_detect.dart';
import 'package:nanami_flutter/common/utils/path.dart';
import 'package:toastification/toastification.dart';
import 'package:ulid/ulid.dart';
import 'package:flutter_sficon/flutter_sficon.dart';

class ManualMaskEditorPage extends StatefulWidget {
  // 待编辑的图片
  final PendingImage pendingImage;

  const ManualMaskEditorPage({super.key, required this.pendingImage});

  @override
  State<ManualMaskEditorPage> createState() => _ManualMaskEditorPageState();
}

class _ManualMaskEditorPageState extends State<ManualMaskEditorPage> {
  // 绘图控制器
  late final DrawingController _drawingController;
  // 变换控制器（用于缩放和平移）
  final TransformationController _transformationController =
      TransformationController();
  // 是否正在加载
  bool _isLoading = true;
  // 画笔大小
  double _brushSize = 10.0;
  // 当前画笔颜色（黑色或白色）
  bool _isBlackBrush = true;
  // 原图透明度
  double _imageOpacity = 0.5;
  // 蒙版路径
  String? _maskPath;
  // 是否可以撤销
  bool _canUndo = false;
  // 是否可以重做
  bool _canRedo = false;

  // 缓存原图尺寸信息
  late int _originalWidth;
  late int _originalHeight;
  late double _aspectRatio;

  @override
  void initState() {
    super.initState();
    _drawingController = DrawingController();
    _drawingController.addListener(_updateUndoRedoState);
    _initMaskEditor();
  }

  // 更新撤销和重做状态
  void _updateUndoRedoState() {
    setState(() {
      _canUndo = _drawingController.canUndo();
      _canRedo = _drawingController.canRedo();
    });
  }

  @override
  void dispose() {
    _drawingController.dispose();
    _transformationController.dispose();
    super.dispose();
  }

  // 初始化蒙版编辑器
  Future<void> _initMaskEditor() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取原图尺寸信息（只读取元数据，不解析整个图片）
      final File originalImageFile = File(widget.pendingImage.filePath);
      final sizeResult = ImageSizeGetter.getSizeResult(
        FileInput(originalImageFile),
      );
      final imageSize = sizeResult.size;

      // 确定图片的实际宽高（考虑旋转）并缓存
      _originalWidth =
          imageSize.needRotate ? imageSize.height : imageSize.width;
      _originalHeight =
          imageSize.needRotate ? imageSize.width : imageSize.height;
      _aspectRatio = _originalWidth / _originalHeight;

      // 如果没有蒙版，则创建一个空白蒙版
      if (widget.pendingImage.maskFilePath == null) {
        final String emptyMaskPath = await HumanFaceDetectUtils.createEmptyMask(
          widget.pendingImage.filePath,
        );
        _maskPath = emptyMaskPath;
      } else {
        _maskPath = widget.pendingImage.maskFilePath;
      }

      // 设置默认画笔为黑色
      _drawingController.setStyle(color: Colors.black, strokeWidth: _brushSize);

      // 设置默认绘制工具为简单线条
      _drawingController.setPaintContent(SimpleLine());

      // 更新撤销和重做状态
      _updateUndoRedoState();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("初始化失败"),
        description: Text("无法加载图片: $e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );

      // 返回上一页
      Get.back();
    }
  }

  // 保存蒙版
  Future<void> _saveMask() async {
    // 显示加载提示
    final toast = toastification.show(
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      title: Text("正在保存"),
      description: Text("请稍候..."),
      alignment: Alignment.topCenter,
      closeButton: ToastCloseButton(showType: CloseButtonShowType.none),
      closeOnClick: false,
      pauseOnHover: false,
    );

    try {
      // 获取绘制结果 - 这里获取的是绘图板当前尺寸的图像数据
      final Uint8List? imageData =
          (await _drawingController.getImageData())?.buffer.asUint8List();

      if (imageData == null) {
        throw Exception("无法获取绘制数据");
      }

      // 生成蒙版图片的保存路径
      String maskPath = '${PathUtils.tempMaskDir}/${Ulid().toString()}.jpg';

      // 使用compute在后台线程处理图像
      final bool success = await compute(_processMaskImageInIsolate, {
        'imageData': imageData,
        'originalWidth': _originalWidth,
        'originalHeight': _originalHeight,
        'maskPath': maskPath,
      });

      if (!success) {
        throw Exception("处理图像失败");
      }

      // 更新蒙版路径
      widget.pendingImage.maskFilePath = maskPath;

      // 关闭加载提示
      toastification.dismiss(toast);

      // 显示成功提示
      toastification.show(
        type: ToastificationType.success,
        style: ToastificationStyle.flat,
        title: Text("保存成功"),
        description: Text("已保存手动标注"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );

      // 返回上一页
      Get.back();
    } catch (e) {
      // 关闭加载提示
      toastification.dismiss(toast);

      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("保存失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
    }
  }

  /// 在独立Isolate中处理蒙版图像
  static Future<bool> _processMaskImageInIsolate(
    Map<String, dynamic> params,
  ) async {
    try {
      final Uint8List imageData = params['imageData'];
      final int originalWidth = params['originalWidth'];
      final int originalHeight = params['originalHeight'];
      final String maskPath = params['maskPath'];

      // 解码绘制结果 - 使用更高效的解码方式
      final img.Image? drawnImage = img.decodeImage(imageData);
      if (drawnImage == null) {
        return false;
      }

      // 优化：检查是否需要调整尺寸
      img.Image finalImage;
      if (drawnImage.width == originalWidth &&
          drawnImage.height == originalHeight) {
        // 尺寸已经匹配，无需调整
        finalImage = drawnImage;
      } else {
        // 计算缩放比例，如果差异很小则不进行调整
        final double widthRatio = originalWidth / drawnImage.width;
        final double heightRatio = originalHeight / drawnImage.height;

        if ((widthRatio - 1.0).abs() < 0.05 &&
            (heightRatio - 1.0).abs() < 0.05) {
          // 尺寸差异小于5%，不进行调整以提高性能
          finalImage = drawnImage;
        } else {
          // 调整图像尺寸以匹配原图 - 使用更快的调整方法
          finalImage = img.copyResize(
            drawnImage,
            width: originalWidth,
            height: originalHeight,
            interpolation: img.Interpolation.nearest, // 使用最近邻插值提高性能
          );
        }
      }

      // 优化：直接编码为JPEG并写入文件
      final List<int> encodedImage = img.encodeJpg(
        finalImage,
        quality: 85, // 适当降低质量以提高性能
      );

      // 保存蒙版图片
      final File maskFile = File(maskPath);
      await maskFile.writeAsBytes(encodedImage);

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text("手动标注"),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _saveMask,
          child: Text("保存"),
        ),
      ),
      child:
          _isLoading
              ? Center(child: CupertinoActivityIndicator())
              : SafeArea(
                child: Column(
                  children: [
                    // 工具栏
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 16,
                      ),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemBackground,
                        border: Border(
                          bottom: BorderSide(
                            color: CupertinoColors.separator,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        spacing: 10,
                        children: [
                          // 画笔工具行
                          Row(
                            children: [
                              // 黑色画笔
                              CupertinoButton(
                                padding: EdgeInsets.all(8),
                                color:
                                    _isBlackBrush
                                        ? CupertinoColors.activeBlue
                                            .resolveFrom(context)
                                            .withOpacity(0.1)
                                        : CupertinoColors.systemBackground,
                                onPressed: () {
                                  setState(() {
                                    _isBlackBrush = true;
                                    _drawingController.setStyle(
                                      color: Colors.black,
                                      strokeWidth: _brushSize,
                                    );
                                  });
                                },
                                child: Icon(
                                  SFIcons.sf_pencil_tip,
                                  color: CupertinoColors.systemGrey,
                                ),
                              ),
                              SizedBox(width: 8),
                              // 白色画笔
                              CupertinoButton(
                                padding: EdgeInsets.all(8),
                                color:
                                    !_isBlackBrush
                                        ? CupertinoColors.activeBlue
                                            .resolveFrom(context)
                                            .withOpacity(0.1)
                                        : CupertinoColors.systemBackground,
                                onPressed: () {
                                  setState(() {
                                    _isBlackBrush = false;
                                    _drawingController.setStyle(
                                      color: Colors.white,
                                      strokeWidth: _brushSize,
                                    );
                                  });
                                },
                                child: Icon(
                                  SFIcons.sf_eraser,
                                  color: CupertinoColors.systemGrey,
                                ),
                              ),
                              SizedBox(width: 23),
                              // 画笔大小滑块
                              Expanded(
                                child: Row(
                                  children: [
                                    Icon(
                                      CupertinoIcons.circle,
                                      size: 12,
                                      color: CupertinoColors.systemGrey,
                                    ),
                                    Expanded(
                                      child: CupertinoSlider(
                                        value: _brushSize,
                                        min: 1.0,
                                        max: 50.0,
                                        onChanged: (value) {
                                          setState(() {
                                            _brushSize = value;
                                            _drawingController.setStyle(
                                              strokeWidth: _brushSize,
                                            );
                                          });
                                        },
                                      ),
                                    ),
                                    Icon(
                                      CupertinoIcons.circle,
                                      size: 24,
                                      color: CupertinoColors.systemGrey,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          // 第二行：撤销、重做、重置和透明度调整
                          Row(
                            children: [
                              // 撤销按钮
                              CupertinoButton(
                                padding: EdgeInsets.all(8),
                                color: CupertinoColors.systemBackground,
                                onPressed:
                                    _canUndo
                                        ? () {
                                          _drawingController.undo();
                                        }
                                        : null,
                                child: Icon(
                                  SFIcons.sf_arrow_uturn_backward,
                                  color:
                                      _canUndo
                                          ? CupertinoColors.systemGrey
                                          : CupertinoColors.systemGrey
                                              .withOpacity(0.3),
                                ),
                              ),
                              SizedBox(width: 8),
                              // 重做按钮
                              CupertinoButton(
                                padding: EdgeInsets.all(8),
                                color: CupertinoColors.systemBackground,
                                onPressed:
                                    _canRedo
                                        ? () {
                                          _drawingController.redo();
                                        }
                                        : null,

                                child: Icon(
                                  SFIcons.sf_arrow_uturn_forward,
                                  color:
                                      _canRedo
                                          ? CupertinoColors.systemGrey
                                          : CupertinoColors.systemGrey
                                              .withOpacity(0.3),
                                ),
                              ),
                              SizedBox(width: 8),
                              // 重置变换按钮
                              CupertinoButton(
                                padding: EdgeInsets.all(8),
                                color: CupertinoColors.systemBackground,
                                onPressed: () {
                                  setState(() {
                                    _transformationController.value =
                                        Matrix4.identity();
                                  });
                                },
                                child: Icon(
                                  CupertinoIcons.arrow_counterclockwise,
                                  color: CupertinoColors.systemGrey,
                                ),
                              ),
                              SizedBox(width: 8),

                              // 透明度文本
                              Text(
                                "预览",
                                style: TextStyle(
                                  color: CupertinoColors.systemGrey,
                                ),
                              ),
                              SizedBox(width: 8),
                              // 透明度滑块
                              Expanded(
                                child: CupertinoSlider(
                                  value: _imageOpacity,
                                  min: 0.0,
                                  max: 1.0,
                                  onChanged: (value) {
                                    setState(() {
                                      _imageOpacity = value;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // 绘图区域
                    Expanded(
                      child: Center(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            // 计算图片在容器中的实际显示尺寸（保持宽高比）
                            double displayWidth = constraints.maxWidth;
                            double displayHeight = constraints.maxHeight;

                            if (constraints.maxWidth / constraints.maxHeight >
                                _aspectRatio) {
                              // 容器更宽，图片高度将填满容器
                              displayWidth =
                                  constraints.maxHeight * _aspectRatio;
                            } else {
                              // 容器更高，图片宽度将填满容器
                              displayHeight =
                                  constraints.maxWidth / _aspectRatio;
                            }

                            return SizedBox(
                              width: constraints.maxWidth,
                              height: constraints.maxHeight,
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  // 绘图板（蒙版在底层）
                                  Center(
                                    child: SizedBox(
                                      width: displayWidth,
                                      height: displayHeight,
                                      child: DrawingBoard(
                                        controller: _drawingController,
                                        transformationController:
                                            _transformationController,
                                        background:
                                            _maskPath != null
                                                ? Image.file(
                                                  File(_maskPath!),
                                                  fit:
                                                      BoxFit
                                                          .fill, // 在确定尺寸的容器中使用fill
                                                  width: displayWidth,
                                                  height: displayHeight,
                                                )
                                                : Container(
                                                  width: displayWidth,
                                                  height: displayHeight,
                                                  color: Colors.white, // 使用白色背景
                                                ),
                                        showDefaultActions: false,
                                        showDefaultTools: false,
                                        boardPanEnabled: true, // 启用画板平移
                                        boardScaleEnabled: true, // 启用画板缩放
                                      ),
                                    ),
                                  ),
                                  // 原图（作为上层，半透明显示）
                                  Center(
                                    child: SizedBox(
                                      width: displayWidth,
                                      height: displayHeight,
                                      child: ClipRect(
                                        child: AnimatedBuilder(
                                          animation: _transformationController,
                                          builder: (context, child) {
                                            return Transform(
                                              transform:
                                                  _transformationController
                                                      .value,
                                              child: IgnorePointer(
                                                // 忽略触摸事件，让事件传递到下方的绘图板
                                                child: Opacity(
                                                  opacity:
                                                      _imageOpacity, // 使用可调整的透明度
                                                  child: Image.file(
                                                    File(
                                                      widget
                                                          .pendingImage
                                                          .filePath,
                                                    ),
                                                    fit:
                                                        BoxFit
                                                            .fill, // 在确定尺寸的容器中使用fill
                                                    width: displayWidth,
                                                    height: displayHeight,
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}
