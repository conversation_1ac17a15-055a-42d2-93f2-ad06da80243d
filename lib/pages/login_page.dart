import 'package:flutter/cupertino.dart';
import 'package:nanami_flutter/common/binding/main_navigation_binding.dart';
import 'package:nanami_flutter/common/utils/permission_util.dart';
import 'main_navigation.dart';
import 'package:lottie/lottie.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 在页面初始化后请求权限
    WidgetsBinding.instance.addPostFrameCallback((_) {
      PermissionUtil.requestAllPermissionsDelayed();
    });
  }

  @override
  void dispose() {
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _login() {
    // 简单登录逻辑，直接导航到主页
    // 先注册必要的依赖
    MainNavigationBinding().dependencies();
    // 使用 Navigator.pushReplacement 替换当前页面
    Navigator.pushReplacement(
      context,
      CupertinoPageRoute(builder: (context) => const MainNavigation()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: CupertinoColors.systemBackground,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 25.0, vertical: 10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 80),
              // 动画
              Center(
                child: Lottie.asset(
                  'assets/lotties/user.json',
                  width: 200,
                  height: 200,
                  frameRate: FrameRate.max,
                ),
              ),

              // 标题
              const Center(
                child: Text(
                  '登录七海账户',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 40),
              // 账户输入框
              CupertinoTextField(
                controller: _accountController,
                placeholder: '账号',
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: CupertinoColors.systemGrey5),
                  ),
                ),
                clearButtonMode: OverlayVisibilityMode.editing,
              ),
              // 密码输入框
              CupertinoTextField(
                controller: _passwordController,
                placeholder: '密码',
                padding: const EdgeInsets.all(16),
                obscureText: true,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: CupertinoColors.systemGrey5),
                  ),
                ),
                clearButtonMode: OverlayVisibilityMode.editing,
              ),
              const SizedBox(height: 40),
              // 登录按钮
              SizedBox(
                width: double.infinity,
                child: CupertinoButton.filled(
                  onPressed: _login,
                  child: const Text('登录'),
                ),
              ),
              const SizedBox(height: 16),
              // 创建账户按钮
              SizedBox(
                width: double.infinity,
                child: CupertinoButton(
                  // color: CupertinoColors.systemGrey6,
                  onPressed: () {},
                  child: Text('创建七海账户'),
                ),
              ),
              const Spacer(),
              // 忘记账户或密码链接
              // Center(
              //   child: Text(
              //     '忘记账号或密码?',
              //     style: TextStyle(color: CupertinoColors.activeBlue),
              //     textAlign: TextAlign.center,
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
