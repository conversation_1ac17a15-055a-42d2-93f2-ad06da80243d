import 'package:bounce/bounce.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/my_image_groups_controller.dart';
import 'package:nanami_flutter/common/models/image_group.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/pages/image_group_detail.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/sheets/add_image_group_sheet.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/widgets/image_group_item.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';

// 创建一个装饰动画，用于自定义阴影效果
final DecorationTween decorationTween = DecorationTween(
  begin: BoxDecoration(
    borderRadius: BorderRadius.circular(18.0),
    boxShadow: const <BoxShadow>[],
  ),
  end: BoxDecoration(
    borderRadius: BorderRadius.circular(18.0),
    boxShadow: CupertinoContextMenu.kEndBoxShadow,
  ),
);

class MyImageGroupsPage extends StatefulWidget {
  const MyImageGroupsPage({super.key});

  @override
  State<MyImageGroupsPage> createState() => _MyImageGroupsPageState();
}

class _MyImageGroupsPageState extends State<MyImageGroupsPage> {
  // 获取控制器
  final MyImageGroupsController controller =
      Get.find<MyImageGroupsController>();

  // 创建一个焦点节点用于控制搜索框的焦点
  final FocusNode _searchFocusNode = FocusNode();

  // 创建一个文本控制器用于搜索框
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    // 释放焦点节点
    _searchFocusNode.dispose();
    // 释放文本控制器
    _searchController.dispose();
    super.dispose();
  }

  // 根据屏幕宽度动态计算宽高比
  double _calculateAspectRatio(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // 计算每个网格项的实际宽度
    // 屏幕宽度 - 左右边距(32) - 两个间距(24) / 3列
    final itemWidth = (screenWidth - 32 - 24) / 3;

    // 根据项目宽度动态调整高度
    // 宽度越大，相对高度应该越小，避免在宽屏设备上过高
    if (itemWidth > 150) {
      // 宽屏设备，使用更大的宽高比（更扁平）
      return 0.8;
    } else if (itemWidth > 120) {
      // 中等屏幕
      return 0.67;
    } else {
      // 小屏设备，使用更小的宽高比（更高）
      return 0.65;
    }
  }

  // 显示重命名对话框
  void _showRenameDialog(BuildContext context, ImageGroupInfo imageGroup) {
    // 创建一个文本控制器，初始值为当前相簿名称
    final TextEditingController textController = TextEditingController(
      text: imageGroup.name.value,
    );

    // 创建一个焦点节点，用于控制键盘焦点
    final FocusNode focusNode = FocusNode();

    // 显示中间模态对话框
    showCupertinoDialog<void>(
      context: context,
      barrierDismissible: true, // 点击背景可关闭对话框
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('重命名相簿'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 当前名称显示
              Padding(
                padding: const EdgeInsets.only(top: 8.0, bottom: 12.0),
                child: Text(
                  imageGroup.name.value,
                  style: TextStyle(
                    color: CupertinoColors.secondaryLabel.resolveFrom(context),
                    fontSize: 13.0,
                  ),
                ),
              ),
              // 输入框
              CupertinoTextField(
                controller: textController,
                focusNode: focusNode,
                placeholder: '相簿新名称',
                clearButtonMode: OverlayVisibilityMode.editing,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemFill.resolveFrom(context),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 8.0,
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            // 取消按钮
            CupertinoDialogAction(
              child: const Text('取消'),
              onPressed: () {
                // 首先收起键盘
                focusNode.unfocus();
                // 然后关闭对话框
                Navigator.of(context).pop();
              },
            ),
            // 确认按钮
            CupertinoDialogAction(
              isDefaultAction: true,
              child: const Text('确认'),

              onPressed: () {
                // 首先收起键盘
                focusNode.unfocus();
                // 获取新名称
                final newName = textController.text.trim();
                // 检查名称是否为空
                if (newName.isEmpty) {
                  // 显示错误提示
                  showCupertinoDialog(
                    context: context,
                    builder:
                        (context) => CupertinoAlertDialog(
                          title: const Text('错误'),
                          content: const Text('相簿名称不能为空。'),
                          actions: [
                            CupertinoDialogAction(
                              child: const Text('好'),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                  );
                  return;
                }

                // 关闭对话框
                Navigator.of(context).pop();

                // 调用重命名方法
                _renameImageGroup(imageGroup, newName);
              },
            ),
          ],
        );
      },
    ).then((_) {
      // 释放资源
      textController.dispose();
      focusNode.dispose();
    });
  }

  // 重命名相簿
  Future<void> _renameImageGroup(
    ImageGroupInfo imageGroup,
    String newName,
  ) async {
    try {
      // 更新相簿名称
      await ImageGroupService.update(imageGroup.id, newName);
      // 更新本地数据
      imageGroup.name.value = newName;
    } catch (e) {
      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 显示错误提示
      showCupertinoDialog(
        context: context,
        builder:
            (context) => CupertinoAlertDialog(
              title: const Text('重命名失败'),
              content: Text('出现错误：$e'),
              actions: [
                CupertinoDialogAction(
                  child: const Text('确定'),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemBackground,
      child: SuperScaffold(
        appBar: SuperAppBar(
          height: 44,
          backgroundColor: MyBgColor.appBarBgColor(context),
          searchBar: SuperSearchBar(
            placeholderText: "搜索",
            cancelButtonText: "取消",
            scrollBehavior: SearchBarScrollBehavior.pinned,
            resultBehavior: SearchBarResultBehavior.neverVisible,
            searchFocusNode: _searchFocusNode, // 使用焦点节点控制搜索框焦点
            searchController: _searchController, // 使用文本控制器
            onChanged: (query) {
              // 当搜索框内容变化时，更新搜索状态
              controller.isSearching.value = query.isNotEmpty;
              // 如果搜索框为空，立即清除搜索结果
              if (query.isEmpty) {
                controller.clearSearch();
              }
            },
            onSubmitted: (query) async {
              // 当用户提交搜索时，执行搜索
              await controller.searchImageGroups(query);
              // 搜索完成后取消焦点
              _searchFocusNode.unfocus();
            },
            onFocused: (focused) {
              controller.logger.d("聚焦状态：$focused");
              // 如果取消搜索
              if (!focused) {
                // 取消搜索
                controller.clearSearch();
              }
            },
          ),
          largeTitle: SuperLargeTitle(largeTitle: "相簿"),
          title: Obx(() {
            // 如果正在搜索，显示搜索结果数量
            if (controller.isSearching.value) {
              return Text("找到 ${controller.imageGroups.length} 个相簿");
            }
            // 否则显示总相簿数量
            else {
              return Text("${controller.imageGroups.length} 个相簿");
            }
          }),
          actions: Padding(
            padding: const EdgeInsets.only(right: 6.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 添加按钮
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  child: const Icon(
                    CupertinoIcons.add,
                    size: 24.0, // 增大图标尺寸
                  ),
                  onPressed: () async {
                    // 弹出添加相簿的模态框
                    final result = await showAddImageGroupSheet(context);
                    // 如果返回结果为true，表示添加了相簿，需要刷新相簿列表
                    if (result == true) {
                      await controller.refreshImageGroups();
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        body: Obx(() {
          // 获取媒体查询的底部内边距
          final bottomPadding = MediaQuery.of(context).padding.bottom;

          // 如果正在加载，显示加载状态
          if (controller.isLoading.value) {
            return const Center(child: CupertinoActivityIndicator(radius: 15));
          }

          // 如果没有相簿，显示空状态
          if (controller.imageGroups.isEmpty) {
            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    CupertinoIcons.rectangle_stack,
                    size: 64,
                    color: MyBgColor.emptyPageIconColor(
                      context,
                    ).resolveFrom(context),
                  ),
                  const SizedBox(height: 12),
                  // 搜索结果未找到的提示
                  controller.isSearching.value
                      ? Text(
                        "未找到相关图片",
                        style: TextStyle(
                          fontSize: 18,
                          color: CupertinoColors.systemGrey.resolveFrom(
                            context,
                          ),
                        ),
                      )
                      : Column(
                        children: [
                          Text(
                            "无相簿",
                            style: TextStyle(
                              fontSize: 21,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "已创建的相簿将在此处显示。",
                            style: TextStyle(
                              fontSize: 15,
                              color: CupertinoColors.secondaryLabel.resolveFrom(
                                context,
                              ),
                            ),
                          ),
                          const SizedBox(height: 44),
                        ],
                      ),
                ],
              ),
            );
          }

          return GridView.builder(
            padding: EdgeInsets.fromLTRB(16, 8, 16, bottomPadding + 10),
            itemCount: controller.imageGroups.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              mainAxisSpacing: 0,
              crossAxisSpacing: 12,
              // 根据屏幕宽度动态计算宽高比
              childAspectRatio: _calculateAspectRatio(context),
            ),
            itemBuilder: (context, index) {
              final imageGroup = controller.imageGroups[index];
              return Column(
                children: [
                  // 相簿预览 - 使用 Flexible 让图片占据大部分空间
                  Flexible(
                    flex: 4, // 图片区域占 4/5 的空间
                    child: CupertinoContextMenu.builder(
                      enableHapticFeedback: true,
                      actions: [
                        CupertinoContextMenuAction(
                          onPressed: () async {
                            // 关闭菜单
                            Navigator.of(context).pop();
                            // 弹出重命名的模态框
                            _showRenameDialog(context, imageGroup);
                          },
                          trailingIcon: CupertinoIcons.pencil,
                          child: const Text('重命名'),
                        ),
                        CupertinoContextMenuAction(
                          onPressed: () async {
                            // 关闭菜单
                            Navigator.of(context).pop();
                            // 删除相簿
                            await ImageGroupService.delete(imageGroup.id);
                            // 刷新相簿列表
                            await controller.refreshImageGroups();
                          },
                          trailingIcon: CupertinoIcons.delete,
                          isDestructiveAction: true,
                          child: const Text('删除'),
                        ),
                      ],
                      builder: (
                        BuildContext context,
                        Animation<double> animation,
                      ) {
                        // 创建动画
                        final Animation<Decoration> boxDecorationAnimation =
                            decorationTween.animate(
                              CurvedAnimation(
                                parent: animation,
                                curve: Interval(
                                  0.0,
                                  CupertinoContextMenu.animationOpensAt,
                                ),
                              ),
                            );

                        bool isNotOpen =
                            animation.value <
                            CupertinoContextMenu.animationOpensAt;

                        return Container(
                          decoration:
                              isNotOpen ? boxDecorationAnimation.value : null,
                          child:
                              isNotOpen
                                  // 未打开的时候正常返回
                                  ? Bounce(
                                    scaleFactor: 0.9,
                                    onTap: () {
                                      // 使用Hero动画过渡到详情页
                                      Future.delayed(
                                        Duration(milliseconds: 45),
                                        () {
                                          Get.to(
                                            () => ImageGroupDetail(imageGroup),
                                            transition:
                                                Transition.cupertinoDialog,
                                          );
                                        },
                                      );
                                    },
                                    child: ImageGroupItem(
                                      imageGroup: imageGroup,
                                      // 为每个相簿创建唯一的Hero标签
                                      heroTag: 'image_group_${imageGroup.id}',
                                    ),
                                  )
                                  // 打开之后限制大小
                                  : SizedBox(
                                    height: 200,
                                    width: 200,
                                    child: ImageGroupItem(
                                      imageGroup: imageGroup,
                                    ),
                                  ),
                        );
                      },
                    ),
                  ),
                  // 文字区域 - 使用 Flexible 占据剩余空间
                  Flexible(
                    flex: 1, // 文字区域占 1/5 的空间
                    child: Padding(
                      padding: const EdgeInsets.only(top: 6), // 添加与上方图片的间距
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          // 相簿名称
                          Obx(
                            () => Text(
                              imageGroup.name.value,
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(height: 1),
                          // 相簿数量
                          Obx(
                            () => Text(
                              "${imageGroup.images.length}",
                              style: TextStyle(
                                fontSize: 11,
                                color: CupertinoColors.secondaryLabel
                                    .resolveFrom(context),
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        }),
      ),
    );
  }
}
