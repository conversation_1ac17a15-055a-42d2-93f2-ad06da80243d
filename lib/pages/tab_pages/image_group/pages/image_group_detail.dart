import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_sficon/flutter_sficon.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/models/image_group.dart';
import 'package:nanami_flutter/common/models/image.dart' as image_model;
import 'package:nanami_flutter/common/services/image.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/pages/tab_pages/image/sheets/select_multiple_images_sheet.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';

class ImageGroupDetail extends StatelessWidget {
  // 相簿对象
  final ImageGroupInfo imageGroup;

  const ImageGroupDetail(this.imageGroup, {super.key});

  @override
  Widget build(BuildContext context) {
    // 获取媒体查询的底部内边距
    final bottomPadding = MediaQuery.of(context).padding.bottom;

    return CupertinoPageScaffold(
      child: Obx(
        () => SuperScaffold(
          appBar: SuperAppBar(
            height: 44,
            backgroundColor: MyBgColor.appBarBgColor(context),
            previousPageTitle: "相簿",
            searchBar: SuperSearchBar(enabled: false),
            largeTitle: SuperLargeTitle(
              enabled: false,
              largeTitle: imageGroup.name.value,
            ),
          ),
          body: Hero(
            tag: 'image_group_${imageGroup.id}',
            child: GridView.builder(
              padding: EdgeInsets.only(top: 8, bottom: bottomPadding),
              itemCount: imageGroup.images.length + 1,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: 1,
                crossAxisSpacing: 1,
              ),

              itemBuilder: (context, index) {
                // 如果是最后一个项，显示添加图片的按钮
                if (index == imageGroup.images.length) {
                  return Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: MyBgColor.addImagePlaceholderBgColor()
                            .resolveFrom(context),
                      ),
                      CupertinoButton(
                        padding: EdgeInsets.zero,
                        onPressed: () async {
                          // 弹出选择图片的 Sheet
                          final selectedIds =
                              await showSelectMultipleImagesSheet(
                                context,
                                title: "选择图片",
                              );
                          // 如果没有选择图片，直接返回
                          if (selectedIds == null) {
                            return;
                          }
                          // 将图片添加到相簿
                          for (var id in selectedIds) {
                            var result =
                                await ImageGroupService.addImageToGroup(
                                  imageGroup.id,
                                  id,
                                );
                            // 如果添加成功，获取图片对象，并添加到相簿对象
                            if (result == true) {
                              final image = await ImageService.getById(id);
                              if (image != null) {
                                imageGroup.images.add(image);
                              }
                            }
                          }
                        },
                        child: Container(
                          width: double.infinity,
                          height: double.infinity,
                          color: CupertinoColors.transparent,
                          child: Center(
                            child: SFIcon(
                              SFIcons.sf_plus,
                              fontSize: 27,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                }

                // 当前图片对象
                var image = imageGroup.images[index];
                // 正常模式下的图片项
                return CupertinoContextMenu(
                  enableHapticFeedback: true,
                  actions: [
                    CupertinoContextMenuAction(
                      onPressed: () async {
                        // 获取该图片 ID
                        final imageId = image.id;
                        // 关闭上下文菜单
                        Navigator.of(context).pop();
                        // 从相簿中删除图片
                        await ImageGroupService.removeImageFromGroup(
                          imageGroup.id,
                          imageId,
                        );
                        // 从当前相簿中移除该图片
                        imageGroup.images.removeWhere(
                          (img) => img.id == imageId,
                        );
                      },
                      trailingIcon: CupertinoIcons.delete,
                      child: const Text('从相簿移除'),
                    ),
                  ],
                  child: ExtendedImage.file(
                    File(image.path),
                    fit: BoxFit.cover,
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
