import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:nanami_flutter/common/models/image.dart' as app_image;
import 'package:nanami_flutter/common/models/image_group.dart';
import 'package:nanami_flutter/common/theme/color.dart';

class ImageGroupItem extends StatelessWidget {
  // 相簿模型
  final ImageGroupInfo imageGroup;

  // 图片间距
  final double spacing;

  // Hero标签
  final String? heroTag;

  const ImageGroupItem({
    super.key,
    required this.imageGroup,
    this.spacing = 2,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = Material(
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      clipBehavior: Clip.antiAlias,
      color: Colors.transparent,
      child: _buildGridLayout(),
    );

    // 如果提供了heroTag，则使用Hero包装
    if (heroTag != null) {
      return Hero(tag: heroTag!, child: content);
    }

    return content;
  }

  // 构建网格布局
  Widget _buildGridLayout() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final itemSize = (constraints.maxWidth - spacing) / 2;

        // 获取相簿中的图片数量，最多显示4张
        final imageCount = imageGroup.images.length;
        final displayCount = imageCount > 4 ? 4 : imageCount;

        return Wrap(
          spacing: spacing,
          runSpacing: spacing,
          children: List.generate(4, (index) {
            // 如果索引小于图片数量，则显示对应的图片
            if (index < displayCount) {
              final app_image.ImageInfo image = imageGroup.images[index];
              return _buildImageItem(context, itemSize, image);
            } else {
              // 否则显示空的容器（只有背景色）
              return _buildEmptyItem(context, itemSize);
            }
          }),
        );
      },
    );
  }

  // 构建单个图片项
  Widget _buildImageItem(
    BuildContext context,
    double itemSize,
    app_image.ImageInfo imageInfo,
  ) {
    return Container(
      width: itemSize,
      height: itemSize,
      decoration: BoxDecoration(
        color: MyBgColor.imageGroupBgColor().resolveFrom(context),
      ),
      child: ExtendedImage.file(
        File(imageInfo.thumbnailPath),
        fit: BoxFit.cover,
      ),
    );
  }

  // 构建空的图片项（只有背景色）
  Widget _buildEmptyItem(BuildContext context, double itemSize) {
    return Container(
      width: itemSize,
      height: itemSize,
      decoration: BoxDecoration(
        color: MyBgColor.imageGroupBgColor().resolveFrom(context),
      ),
    );
  }
}
