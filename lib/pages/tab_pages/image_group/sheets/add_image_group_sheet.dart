import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/widgets/cupertino_sheet_top_bar.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

Future<bool?> showAddImageGroupSheet(BuildContext context) {
  return Navigator.push<bool>(
    context,
    CupertinoModalSheetRoute(
      // 启用滑动关闭功能，用户可以通过向下滑动来关闭sheet
      swipeDismissible: true,
      builder: (context) {
        return const AddImageGroupSheet();
      },
    ),
  );
}

class AddImageGroupSheet extends StatefulWidget {
  const AddImageGroupSheet({super.key});

  @override
  State<AddImageGroupSheet> createState() => _AddImageGroupSheetState();
}

class _AddImageGroupSheetState extends State<AddImageGroupSheet> {
  // 文本控制器
  final TextEditingController _nameController = TextEditingController();
  // 使用 GetX 响应式变量跟踪文本域是否为空
  final RxBool _isTextEmpty = true.obs;

  @override
  void initState() {
    super.initState();
    // 添加监听器来检测文本变化
    _nameController.addListener(_updateTextStatus);
  }

  @override
  void dispose() {
    // 移除监听器
    _nameController.removeListener(_updateTextStatus);
    // 释放控制器资源
    _nameController.dispose();

    super.dispose();
  }

  // 更新文本状态的方法
  void _updateTextStatus() {
    final isEmpty = _nameController.text.trim().isEmpty;
    // 使用 GetX 的方式更新响应式变量
    if (isEmpty != _isTextEmpty.value) {
      _isTextEmpty.value = isEmpty;
    }
  }

  Future<void> _createImageGroup() async {
    try {
      // 由于按钮在文本为空时已被禁用，这里可以直接处理创建逻辑
      // 获取相簿名称
      final groupName = _nameController.text.trim();

      // 创建相簿
      await ImageGroupService.add(groupName);

      // 检查组件是否仍然挂载
      if (mounted) {
        // 关闭当前页面，并返回成功标志
        Navigator.pop(context, true);
      }
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("创建相簿失败: $e");

      // 检查组件是否仍然挂载
      if (mounted) {
        // 关闭当前页面，并返回失败标志
        Navigator.pop(context, false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SheetKeyboardDismissible(
      dismissBehavior: const SheetKeyboardDismissBehavior.onDragDown(),
      child: Sheet(
        // Sheet 装饰器
        decoration: SheetDecorationBuilder(
          size: SheetSize.stretch,
          builder: (context, child) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: ColoredBox(
                color: CupertinoColors.systemGrey6.resolveFrom(context),
                child: child,
              ),
            );
          },
        ),
        child: SheetContentScaffold(
          backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
          topBar: CupertinoSheetTopBar(
            title: Text(
              "新建相簿",
              // style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            leading: CupertinoButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            trailing: Obx(
              () => CupertinoButton(
                // 当文本为空时禁用按钮
                onPressed:
                    _isTextEmpty.value
                        ? null
                        : () async {
                          await _createImageGroup();
                        },
                child: Text(
                  '创建',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ),
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // 相簿名称输入框
                Material(
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: CupertinoTextField(
                    controller: _nameController,
                    // 自动获取焦点
                    autofocus: true,
                    placeholder: '相簿名称',
                    // 添加粗体文本样式
                    style: const TextStyle(fontWeight: FontWeight.bold),
                    // 添加粗体占位符文本样式
                    placeholderStyle: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: CupertinoColors.placeholderText,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 18.0,
                    ),
                    decoration: BoxDecoration(
                      color: MyBgColor.sheetTextFieldBgColor().resolveFrom(
                        context,
                      ),
                      // borderRadius: BorderRadius.circular(8.0),
                    ),
                    clearButtonMode: OverlayVisibilityMode.editing,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
