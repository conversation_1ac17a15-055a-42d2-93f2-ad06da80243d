import 'package:bounce/bounce.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:nanami_flutter/common/controllers/my_image_groups_controller.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/sheets/add_image_group_sheet.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/widgets/image_group_item.dart';
import 'package:nanami_flutter/widgets/cupertino_sheet_top_bar.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

// 选择单个相簿的 Sheet，返回相簿 ID 或 null
Future<int?> showSelectImageGroupSheet(
  BuildContext context, {
  String? title,
}) async {
  var result = await Navigator.push<int?>(
    context,
    CupertinoModalSheetRoute(
      // 启用滑动关闭功能，用户可以通过向下滑动来关闭sheet
      swipeDismissible: true,
      builder: (context) {
        return SelectImageGroupSheet(title: title ?? "选择相簿");
      },
    ),
  );
  // 获取控制器
  final MyImageGroupsController controller =
      Get.find<MyImageGroupsController>();
  // 200 毫秒后，清空搜索结果
  Future.delayed(Duration(milliseconds: 200), () {
    controller.clearSearch();
  });

  LoggerUtil.logger.d("选择相簿结果: $result");
  return result;
}

class SelectImageGroupSheet extends StatefulWidget {
  // Sheet 标题
  final String title;

  const SelectImageGroupSheet({super.key, this.title = "选择相簿"});

  @override
  State<SelectImageGroupSheet> createState() => _AddImageGroupSheetState();
}

class _AddImageGroupSheetState extends State<SelectImageGroupSheet> {
  // 获取控制器
  final MyImageGroupsController controller =
      Get.find<MyImageGroupsController>();

  // 创建一个焦点节点用于控制搜索框的焦点
  final FocusNode _searchFocusNode = FocusNode();

  // 创建一个文本控制器用于搜索框
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 添加焦点监听
    _searchFocusNode.addListener(_onFocusChange);
  }

  // 焦点变化处理
  void _onFocusChange() {
    // 如果失去焦点且搜索框为空，清除搜索状态
    if (!_searchFocusNode.hasFocus && _searchController.text.isEmpty) {
      controller.isSearching.value = false;
    }
  }

  @override
  void dispose() {
    // 移除焦点监听
    _searchFocusNode.removeListener(_onFocusChange);
    // 释放焦点节点
    _searchFocusNode.dispose();
    // 释放文本控制器
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SheetKeyboardDismissible(
      dismissBehavior: const SheetKeyboardDismissBehavior.onDragDown(),
      child: Sheet(
        // Sheet 装饰器
        decoration: SheetDecorationBuilder(
          size: SheetSize.stretch,
          builder: (context, child) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: ColoredBox(
                color: CupertinoColors.secondarySystemGroupedBackground
                    .resolveFrom(context),
                child: child,
              ),
            );
          },
        ),
        child: SheetContentScaffold(
          backgroundColor: CupertinoColors.secondarySystemGroupedBackground
              .resolveFrom(context),
          topBar: CupertinoSheetTopBar(
            backgroundColor: Colors.transparent,
            title: Text(widget.title),
            leading: CupertinoButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            trailing: // 添加按钮
                CupertinoButton(
              padding: EdgeInsets.zero,
              child: const Icon(
                CupertinoIcons.add,
                size: 24.0, // 增大图标尺寸
              ),
              onPressed: () async {
                // 弹出添加相簿的模态框
                final result = await showAddImageGroupSheet(context);
                // 如果返回结果为true，表示添加了相簿，需要刷新相簿列表
                if (result == true) {
                  await controller.refreshImageGroups();
                }
              },
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
            child: Stack(
              children: [
                Obx(() {
                  // 获取媒体查询的底部内边距
                  final bottomPadding = MediaQuery.of(context).padding.bottom;

                  // 如果正在加载，显示加载状态
                  if (controller.isLoading.value) {
                    return const Center(
                      child: CupertinoActivityIndicator(radius: 15),
                    );
                  }

                  // 如果没有相簿，显示空状态
                  if (controller.imageGroups.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        // mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            CupertinoIcons.rectangle_stack,
                            size: 64,
                            color: MyBgColor.emptyPageIconColor(
                              context,
                            ).resolveFrom(context),
                          ),
                          const SizedBox(height: 12),
                          // 搜索结果未找到的提示
                          controller.isSearching.value
                              ? Text(
                                "未找到相关图片",
                                style: TextStyle(
                                  fontSize: 18,
                                  color: CupertinoColors.systemGrey.resolveFrom(
                                    context,
                                  ),
                                ),
                              )
                              : Column(
                                children: [
                                  Text(
                                    "无相簿",
                                    style: TextStyle(
                                      fontSize: 21,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    "已创建的相簿将在此处显示。",
                                    style: TextStyle(
                                      fontSize: 15,
                                      color: CupertinoColors.secondaryLabel
                                          .resolveFrom(context),
                                    ),
                                  ),
                                ],
                              ),
                        ],
                      ),
                    );
                  }
                  return GridView.builder(
                    // 顶部留出足够空间给搜索框
                    padding: EdgeInsets.only(top: 60, bottom: bottomPadding),
                    itemCount: controller.imageGroups.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          mainAxisSpacing: 20,
                          crossAxisSpacing: 12,
                          // 设置子项的宽高比为1:1.4，这样下方有足够空间显示文本
                          childAspectRatio: 1 / 1.4,
                        ),
                    itemBuilder: (context, index) {
                      final imageGroup = controller.imageGroups[index];
                      return Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 相簿预览
                          Expanded(
                            child: Bounce(
                              scaleFactor: 0.90,
                              child: ImageGroupItem(imageGroup: imageGroup),
                              onTap: () {
                                // 关闭当前页面，并返回相簿ID
                                Navigator.pop(context, imageGroup.id);
                              },
                            ),
                          ),
                          // 相簿名称
                          const SizedBox(height: 10),
                          Obx(
                            () => Text(
                              imageGroup.name.value,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          // 相簿数量
                          Obx(
                            () => Text(
                              "${imageGroup.images.length}",
                              style: TextStyle(
                                fontSize: 14,
                                color: CupertinoColors.secondaryLabel
                                    .resolveFrom(context),
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      );
                    },
                  );
                }),
                // 搜索栏叠加在gridview上方
                Positioned(
                  top: 8,
                  left: 0,
                  right: 0,
                  child: Material(
                    color: Colors.transparent,
                    shape: RoundedSuperellipseBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    clipBehavior: Clip.antiAlias,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: CupertinoColors.systemGrey5
                              .resolveFrom(context)
                              .withOpacity(0.5), // 浅灰色边框，更贴合 iOS 风格
                          width: 0.5,
                        ),
                        borderRadius: BorderRadius.circular(8.0), // 保持圆角一致
                      ),
                      child: CupertinoSearchTextField(
                        placeholder: "搜索",
                        backgroundColor: CupertinoColors.systemGrey3
                            .resolveFrom(context)
                            .withOpacity(0.1),
                        focusNode: _searchFocusNode,
                        controller: _searchController,
                        onChanged: (query) {
                          // 当搜索框内容变化时，更新搜索状态
                          controller.isSearching.value = query.isNotEmpty;
                          // 如果搜索框为空，立即清除搜索结果
                          if (query.isEmpty) {
                            controller.clearSearch();
                          }
                        },
                        onSubmitted: (query) async {
                          // 当用户提交搜索时，执行搜索
                          await controller.searchImageGroups(query);
                          // 搜索完成后取消焦点
                          _searchFocusNode.unfocus();
                        },
                        onSuffixTap: () {
                          // 清空搜索框
                          _searchController.clear();
                          // 清除搜索
                          controller.clearSearch();
                          // 取消焦点
                          _searchFocusNode.unfocus();
                        },
                      ).asGlass(blurX: 60, blurY: 60),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
