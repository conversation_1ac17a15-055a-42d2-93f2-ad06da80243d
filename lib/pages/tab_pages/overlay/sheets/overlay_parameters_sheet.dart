import 'package:flutter/cupertino.dart';
import 'package:flutter_sficon/flutter_sficon.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/widgets/cupertino_sheet_top_bar.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

void showOverlayParametersSheet(BuildContext context) {
  // 路由 push 一个 sheetRoute
  Navigator.push(
    context,
    CupertinoModalSheetRoute(
      swipeDismissible: true,
      builder: (context) {
        return OverlayParametersSheet();
      },
    ),
  );
}

class OverlayParametersSheet extends StatelessWidget {
  OverlayParametersSheet({super.key});

  // 创建一个嵌套导航器，用于在sheet内部进行页面导航
  final Navigator _nestedNavigator = Navigator(
    onGenerateInitialRoutes: (navigator, initialRoute) {
      return [
        // 使用 PagedSheetRoute 作为初始路由
        PagedSheetRoute(
          builder: (context) {
            // 返回初始页面内容
            return _OverlayParametersSheetContent();
          },
          // 设置初始偏移量为0.5，表示sheet初始时显示在屏幕高度的一半处
          initialOffset: const SheetOffset(0.6),
          // 设置吸附网格，定义sheet可以吸附的位置
          snapGrid: const SheetSnapGrid(
            // 定义三个吸附点：半屏(0.6) 和全屏(1)
            snaps: [SheetOffset(0.6), SheetOffset(1)],
          ),
        ),
      ];
    },
  );

  @override
  Widget build(BuildContext context) {
    return PagedSheet(
      // 导航器
      navigator: _nestedNavigator,
      // 装饰器
      decoration: SheetDecorationBuilder(
        size: SheetSize.stretch,
        builder: (context, child) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: ColoredBox(
              color: CupertinoColors.systemGrey6.resolveFrom(context),
              child: child,
            ),
          );
        },
      ),
    );
  }
}

class _OverlayParametersSheetContent extends StatelessWidget {
  const _OverlayParametersSheetContent({super.key});

  // 根据索引和总数获取圆角
  BorderRadius _getBorderRadius(int index, int total) {
    if (total == 1) {
      // 只有一个元素时，四个角都圆角
      return BorderRadius.circular(8);
    } else if (index == 0) {
      // 第一个元素，只有上面圆角
      return const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      );
    } else if (index == total - 1) {
      // 最后一个元素，只有下面圆角
      return const BorderRadius.only(
        bottomLeft: Radius.circular(8),
        bottomRight: Radius.circular(8),
      );
    } else {
      // 中间元素，没有圆角
      return BorderRadius.zero;
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = SettingController.to;

    return SheetContentScaffold(
      topBar: CupertinoSheetTopBar(
        title: Text('贴膜参数'),
        trailing: CupertinoButton(
          child: Text(
            "完成",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          onPressed: () {
            // 关闭整个 sheet 的方法
            // 使用 Navigator.of(context, rootNavigator: true) 获取根导航器
            // 这样可以确保关闭整个 sheet，而不仅仅是当前页面
            Navigator.of(context, rootNavigator: true).pop();
          },
        ),
      ),
      backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
      body: Column(
        children: [
          // 自动擦脸
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            children: [
              Container(
                decoration: BoxDecoration(
                  color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                  borderRadius: _getBorderRadius(0, 1), // 只有一个元素，使用四个圆角
                ),
                child: CupertinoFormRow(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 2.5,
                  ),
                  prefix: Text('自动擦脸'),
                  child: Obx(
                    () => CupertinoSwitch(
                      value: controller.overlayAutoCleanFace.value,
                      onChanged:
                          (value) =>
                              controller.overlayAutoCleanFace.value = value,
                    ),
                  ),
                ),
              ),
            ],
          ),

          // 混合模式和不透明度
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            children: [
              // 混合模式
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => _navigateToBlendModePage(context),
                child: Container(
                  decoration: BoxDecoration(
                    color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                    borderRadius: _getBorderRadius(0, 2), // 第一个元素，只有上面圆角
                  ),
                  child: CupertinoFormRow(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    prefix: Text('混合模式'),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Obx(() {
                          String modeName;
                          switch (controller.overlayBlendMode.value) {
                            case 'normal':
                              modeName = '混合模式';
                              break;
                            case 'overlay':
                              modeName = '叠加';
                              break;
                            case 'soft_light':
                              modeName = '柔光';
                              break;
                            case 'screen':
                              modeName = '滤色';
                              break;
                            case 'multiply':
                              modeName = '正片叠底';
                              break;
                            default:
                              modeName = '正常';
                          }
                          return Text(
                            modeName,
                            style: TextStyle(
                              fontSize: 17,
                              color: CupertinoColors.secondaryLabel.resolveFrom(
                                context,
                              ),
                              inherit: false,
                            ),
                          );
                        }),
                        const SizedBox(width: 7),
                        Icon(
                          CupertinoIcons.chevron_right,
                          size: 18,
                          color: CupertinoColors.tertiaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // 不透明度
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => _navigateToOpacityPage(context),
                child: Container(
                  decoration: BoxDecoration(
                    color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                    borderRadius: _getBorderRadius(1, 2), // 最后一个元素，只有下面圆角
                  ),
                  child: CupertinoFormRow(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    prefix: Text('不透明度'),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Obx(
                          () => Text(
                            '${controller.overlayOpacity.value}%',
                            style: TextStyle(
                              fontSize: 17,
                              color: CupertinoColors.systemGrey.resolveFrom(
                                context,
                              ),
                              inherit: false,
                            ),
                          ),
                        ),
                        const SizedBox(width: 7),
                        Icon(
                          CupertinoIcons.chevron_right,
                          size: 18,
                          color: CupertinoColors.tertiaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          // 填充方式
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            children: [
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => _navigateToFillModePage(context),
                child: Container(
                  decoration: BoxDecoration(
                    color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                    borderRadius: _getBorderRadius(0, 1), // 只有一个元素，使用四个圆角
                  ),
                  child: CupertinoFormRow(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    prefix: Text('填充方式'),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Obx(() {
                          String fillModeName;
                          switch (controller.overlayFillMode.value) {
                            case 'stretch':
                              fillModeName = '拉伸';
                              break;
                            case 'tile':
                              fillModeName = '平铺';
                              break;
                            default:
                              fillModeName = '拉伸';
                          }
                          return Text(
                            fillModeName,
                            style: TextStyle(
                              fontSize: 17,
                              color: CupertinoColors.systemGrey.resolveFrom(
                                context,
                              ),
                              inherit: false,
                            ),
                          );
                        }),
                        const SizedBox(width: 8),
                        Icon(
                          CupertinoIcons.chevron_right,
                          size: 18,
                          color: CupertinoColors.tertiaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          // 输出格式
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            children: [
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => _navigateToOutputFormatPage(context),
                child: Container(
                  decoration: BoxDecoration(
                    color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                    borderRadius: _getBorderRadius(0, 1), // 只有一个元素，使用四个圆角
                  ),
                  child: CupertinoFormRow(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 10,
                    ),
                    prefix: Text('输出格式'),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Obx(
                          () => Text(
                            controller.overlayOutputFormat.value.toUpperCase(),
                            style: TextStyle(
                              fontSize: 17,
                              color: CupertinoColors.systemGrey.resolveFrom(
                                context,
                              ),
                              inherit: false,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          CupertinoIcons.chevron_right,
                          size: 18,
                          color: CupertinoColors.tertiaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 导航到混合模式页面
  void _navigateToBlendModePage(BuildContext context) {
    Navigator.push(
      context,
      PagedSheetRoute(
        builder: (context) => _BlendModePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // 使用 CupertinoPageTransition 的动画
          return CupertinoPageTransition(
            primaryRouteAnimation: animation,
            secondaryRouteAnimation: secondaryAnimation,
            linearTransition: false,
            child: child,
          );
        },
      ),
    );
  }

  // 导航到不透明度页面
  void _navigateToOpacityPage(BuildContext context) {
    Navigator.push(
      context,
      PagedSheetRoute(
        builder: (context) => _OpacityPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          // 使用 CupertinoPageTransition 的动画
          return CupertinoPageTransition(
            primaryRouteAnimation: animation,
            secondaryRouteAnimation: secondaryAnimation,
            linearTransition: false,
            child: child,
          );
        },
      ),
    );
  }

  // 导航到填充方式页面
  void _navigateToFillModePage(BuildContext context) {
    Navigator.push(
      context,
      PagedSheetRoute(
        builder: (context) => _FillModePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return CupertinoPageTransition(
            primaryRouteAnimation: animation,
            secondaryRouteAnimation: secondaryAnimation,
            linearTransition: false,
            child: child,
          );
        },
      ),
    );
  }

  // 导航到输出格式页面
  void _navigateToOutputFormatPage(BuildContext context) {
    Navigator.push(
      context,
      PagedSheetRoute(
        builder: (context) => _OutputFormatPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return CupertinoPageTransition(
            primaryRouteAnimation: animation,
            secondaryRouteAnimation: secondaryAnimation,
            linearTransition: false,
            child: child,
          );
        },
      ),
    );
  }
}

// 子页面的返回上一页按钮
class _ChildPageLeading extends StatelessWidget {
  const _ChildPageLeading({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: () => Navigator.pop(context),
      child: Row(
        children: [
          Icon(CupertinoIcons.back, size: 28),
          const SizedBox(width: 2.5),
          Text('贴膜参数', style: TextStyle(fontSize: 16)),
        ],
      ),
    );
  }
}

// 混合模式页面
class _BlendModePage extends StatelessWidget {
  // 根据索引和总数获取圆角
  BorderRadius _getBorderRadius(int index, int total) {
    if (total == 1) {
      // 只有一个元素时，四个角都圆角
      return BorderRadius.circular(8);
    } else if (index == 0) {
      // 第一个元素，只有上面圆角
      return const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      );
    } else if (index == total - 1) {
      // 最后一个元素，只有下面圆角
      return const BorderRadius.only(
        bottomLeft: Radius.circular(8),
        bottomRight: Radius.circular(8),
      );
    } else {
      // 中间元素，没有圆角
      return BorderRadius.zero;
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = SettingController.to;

    return SheetContentScaffold(
      topBar: CupertinoSheetTopBar(
        title: Text('混合模式'),
        leading: const _ChildPageLeading(),
      ),
      backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
      body: Column(
        children: [
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            footer: Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                "“叠加” 与 “柔光” 适用于浮雕水印。",
                style: TextStyle(
                  color: CupertinoColors.secondaryLabel.resolveFrom(context),
                ),
              ),
            ),
            children: [
              _buildBlendModeOption(context, '正常', 'normal', 0, 5),
              _buildBlendModeOption(context, '叠加', 'overlay', 1, 5),
              _buildBlendModeOption(context, '柔光', 'soft_light', 2, 5),
              _buildBlendModeOption(context, '滤色', 'screen', 3, 5),
              _buildBlendModeOption(context, '正片叠底', 'multiply', 4, 5),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBlendModeOption(
    BuildContext context,
    String title,
    String mode,
    int index,
    int total,
  ) {
    final controller = SettingController.to;

    return Obx(() {
      final isSelected = controller.overlayBlendMode.value == mode;

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          controller.overlayBlendMode.value = mode;
        },
        child: Container(
          decoration: BoxDecoration(
            color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
            borderRadius: _getBorderRadius(index, total),
          ),
          child: CupertinoFormRow(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 17,
                    inherit: false,
                    color: CupertinoColors.label.resolveFrom(context),
                  ),
                ),
                isSelected
                    ? SFIcon(
                      SFIcons.sf_checkmark,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: CupertinoTheme.of(context).primaryColor,
                    )
                    : const SizedBox(width: 20),
              ],
            ),
          ),
        ),
      );
    });
  }
}

// 不透明度页面
class _OpacityPage extends StatelessWidget {
  // 根据索引和总数获取圆角
  BorderRadius _getBorderRadius(int index, int total) {
    if (total == 1) {
      // 只有一个元素时，四个角都圆角
      return BorderRadius.circular(8);
    } else if (index == 0) {
      // 第一个元素，只有上面圆角
      return const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      );
    } else if (index == total - 1) {
      // 最后一个元素，只有下面圆角
      return const BorderRadius.only(
        bottomLeft: Radius.circular(8),
        bottomRight: Radius.circular(8),
      );
    } else {
      // 中间元素，没有圆角
      return BorderRadius.zero;
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = SettingController.to;

    return SheetContentScaffold(
      topBar: CupertinoSheetTopBar(
        title: Text('不透明度'),
        leading: const _ChildPageLeading(),
      ),
      backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
      body: Column(
        children: [
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            footer: Padding(
              padding: const EdgeInsets.only(left: 20),
              child: Text(
                "水印叠加在底图上的不透明度。",
                style: TextStyle(
                  color: CupertinoColors.secondaryLabel.resolveFrom(context),
                ),
              ),
            ),
            children: [
              Container(
                decoration: BoxDecoration(
                  color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                  borderRadius: _getBorderRadius(0, 1), // 只有一个元素，使用四个圆角
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 10, 20, 8),
                  child: Column(
                    children: [
                      CupertinoFormRow(
                        prefix: Text('不透明度'),
                        padding: EdgeInsets.zero,
                        child: Obx(
                          () => Text(
                            '${controller.overlayOpacity.value}%',
                            style: TextStyle(
                              fontSize: 17,
                              inherit: false,
                              color: CupertinoColors.label.resolveFrom(context),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),
                      Obx(
                        () => SizedBox(
                          width: double.infinity,
                          child: CupertinoSlider(
                            value: controller.overlayOpacity.value.toDouble(),
                            min: 0,
                            max: 100,
                            divisions: 100,
                            onChanged: (value) {
                              controller.overlayOpacity.value = value.toInt();
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// 填充方式页面
class _FillModePage extends StatelessWidget {
  // 根据索引和总数获取圆角
  BorderRadius _getBorderRadius(int index, int total) {
    if (total == 1) {
      // 只有一个元素时，四个角都圆角
      return BorderRadius.circular(8);
    } else if (index == 0) {
      // 第一个元素，只有上面圆角
      return const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      );
    } else if (index == total - 1) {
      // 最后一个元素，只有下面圆角
      return const BorderRadius.only(
        bottomLeft: Radius.circular(8),
        bottomRight: Radius.circular(8),
      );
    } else {
      // 中间元素，没有圆角
      return BorderRadius.zero;
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = SettingController.to;

    return SheetContentScaffold(
      topBar: CupertinoSheetTopBar(
        title: Text('填充方式'),
        leading: const _ChildPageLeading(),
      ),
      backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
      body: Column(
        children: [
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            children: [
              _buildFillModeOption(
                context,
                '拉伸',
                'stretch',
                0,
                2,
                subtitle: '水印将会拉伸以适应底图尺寸',
              ),
              _buildFillModeOption(
                context,
                '平铺',
                'tile',
                1,
                2,
                subtitle: '水印将会在底图上重复平铺',
              ),
            ],
          ),

          // 平铺倍率（仅在选择平铺时显示）
          Obx(() {
            if (controller.overlayFillMode.value == 'tile') {
              return CupertinoFormSection.insetGrouped(
                backgroundColor: CupertinoColors.systemGrey6.resolveFrom(
                  context,
                ),
                header: const Padding(
                  padding: EdgeInsets.only(left: 20),
                  child: Text('平铺倍率'),
                ),
                footer: Padding(
                  padding: const EdgeInsets.only(left: 20),
                  child: Text(
                    "水印的尺寸将会以指定倍率缩放后平铺。",
                    style: TextStyle(
                      color: CupertinoColors.secondaryLabel.resolveFrom(
                        context,
                      ),
                    ),
                  ),
                ),
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: MyBgColor.sheetFormRowBgColor().resolveFrom(
                        context,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 10, 20, 8),
                      child: Column(
                        children: [
                          CupertinoFormRow(
                            prefix: Text('缩放比例'),
                            padding: EdgeInsets.zero,
                            child: Obx(
                              () => Text(
                                '${controller.overlayTileRate.value}%',
                                style: TextStyle(
                                  fontSize: 17,
                                  inherit: false,
                                  color: CupertinoColors.label.resolveFrom(
                                    context,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 10),
                          Obx(
                            () => SizedBox(
                              width: double.infinity,
                              child: CupertinoSlider(
                                value:
                                    controller.overlayTileRate.value.toDouble(),
                                min: 50,
                                max: 200,
                                divisions: 150,
                                onChanged: (value) {
                                  controller.overlayTileRate.value =
                                      value.toInt();
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            } else {
              return const SizedBox.shrink();
            }
          }),
        ],
      ),
    );
  }

  Widget _buildFillModeOption(
    BuildContext context,
    String title,
    String mode,
    int index,
    int total, {
    String? subtitle,
  }) {
    final controller = SettingController.to;

    return Obx(() {
      final isSelected = controller.overlayFillMode.value == mode;

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          controller.overlayFillMode.value = mode;
        },
        child: Container(
          decoration: BoxDecoration(
            color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
            borderRadius: _getBorderRadius(index, total),
          ),
          child: CupertinoFormRow(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 17,
                        inherit: false,
                        color: CupertinoColors.label.resolveFrom(context),
                      ),
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: CupertinoColors.secondaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ),
                  ],
                ),
                isSelected
                    ? SFIcon(
                      SFIcons.sf_checkmark,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: CupertinoTheme.of(context).primaryColor,
                    )
                    : const SizedBox(width: 20),
              ],
            ),
          ),
        ),
      );
    });
  }
}

// 输出格式页面
class _OutputFormatPage extends StatelessWidget {
  // 根据索引和总数获取圆角
  BorderRadius _getBorderRadius(int index, int total) {
    if (total == 1) {
      // 只有一个元素时，四个角都圆角
      return BorderRadius.circular(8);
    } else if (index == 0) {
      // 第一个元素，只有上面圆角
      return const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      );
    } else if (index == total - 1) {
      // 最后一个元素，只有下面圆角
      return const BorderRadius.only(
        bottomLeft: Radius.circular(8),
        bottomRight: Radius.circular(8),
      );
    } else {
      // 中间元素，没有圆角
      return BorderRadius.zero;
    }
  }

  @override
  Widget build(BuildContext context) {
    final controller = SettingController.to;

    return SheetContentScaffold(
      topBar: CupertinoSheetTopBar(
        title: Text('输出格式'),
        leading: const _ChildPageLeading(),
      ),
      backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
      body: Column(
        children: [
          CupertinoFormSection.insetGrouped(
            backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
            header: Padding(padding: const EdgeInsets.only(left: 20)),
            children: [
              _buildOutputFormatOption(
                context,
                'PNG',
                'png',
                0,
                2,
                subtitle: '无损压缩，文件体积较大，支持透明背景',
              ),
              _buildOutputFormatOption(
                context,
                'JPG',
                'jpg',
                1,
                2,
                subtitle: '有损压缩，文件体积较小，不支持透明背景',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOutputFormatOption(
    BuildContext context,
    String title,
    String format,
    int index,
    int total, {
    String? subtitle,
  }) {
    final controller = SettingController.to;

    return Obx(() {
      final isSelected = controller.overlayOutputFormat.value == format;

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          controller.overlayOutputFormat.value = format;
        },
        child: Container(
          decoration: BoxDecoration(
            color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
            borderRadius: _getBorderRadius(index, total),
          ),
          child: CupertinoFormRow(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 17,
                        inherit: false,
                        color: CupertinoColors.label.resolveFrom(context),
                      ),
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: CupertinoColors.secondaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ),
                  ],
                ),
                isSelected
                    ? SFIcon(
                      SFIcons.sf_checkmark,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: CupertinoTheme.of(context).primaryColor,
                    )
                    : const SizedBox(width: 20),
              ],
            ),
          ),
        ),
      );
    });
  }
}
