import 'package:flutter/cupertino.dart';
import 'package:flutter_sficon/flutter_sficon.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/widgets/cupertino_sheet_top_bar.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

void showOverlaySettingsSheet(BuildContext context) {
  // 路由 push 一个 sheetRoute
  Navigator.push(
    context,
    CupertinoModalSheetRoute(
      swipeDismissible: true,
      builder: (context) {
        return OverlaySettingsSheet();
      },
    ),
  );
}

class OverlaySettingsSheet extends StatelessWidget {
  OverlaySettingsSheet({super.key});

  // 设置控制器
  final SettingController settingController = Get.find<SettingController>();

  // 显示分类规则（如果开启自动分类为 true，否则为 false）
  bool get showClassifyRule => settingController.overlayAutoClassify.value;

  // 根据索引和总数获取圆角
  BorderRadius _getBorderRadius(int index, int total) {
    if (total == 1) {
      // 只有一个元素时，四个角都圆角
      return BorderRadius.circular(8);
    } else if (index == 0) {
      // 第一个元素，只有上面圆角
      return const BorderRadius.only(
        topLeft: Radius.circular(8),
        topRight: Radius.circular(8),
      );
    } else if (index == total - 1) {
      // 最后一个元素，只有下面圆角
      return const BorderRadius.only(
        bottomLeft: Radius.circular(8),
        bottomRight: Radius.circular(8),
      );
    } else {
      // 中间元素，没有圆角
      return BorderRadius.zero;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Sheet(
      initialOffset: const SheetOffset(0.58),
      snapGrid: const SheetSnapGrid(snaps: [SheetOffset(0.58), SheetOffset(1)]),
      decoration: SheetDecorationBuilder(
        size: SheetSize.stretch,
        builder: (context, child) {
          return ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: ColoredBox(
              color: CupertinoColors.systemGrey6.resolveFrom(context),
              child: child,
            ),
          );
        },
      ),
      child: SheetContentScaffold(
        backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
        topBar: CupertinoSheetTopBar(
          title: Text("贴膜设置"),
          trailing: CupertinoButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              '完成',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          ),
        ),
        body: Column(
          children: [
            Obx(() {
              // 构建子元素列表
              final List<Widget> formChildren = [
                CupertinoFormRow(
                  prefix: Text("自动分类"),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 2.5,
                  ),
                  child: CupertinoSwitch(
                    value: settingController.overlayAutoClassify.value,
                    onChanged: (value) {
                      settingController.overlayAutoClassify.value = value;
                    },
                  ),
                ),
                if (showClassifyRule)
                  CupertinoFormRow(
                    prefix: Text("分类规则"),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 0,
                    ),
                    child: PullDownButton(
                      itemBuilder: (context) {
                        return [
                          PullDownMenuItem.selectable(
                            title: '水印',
                            onTap: () {
                              settingController.overlayAutoClassifyBy.value =
                                  'watermark';
                            },
                            selected:
                                settingController.overlayAutoClassifyBy.value ==
                                'watermark',
                          ),
                          PullDownMenuItem.selectable(
                            title: '底图',
                            onTap: () {
                              settingController.overlayAutoClassifyBy.value =
                                  'base_image';
                            },
                            selected:
                                settingController.overlayAutoClassifyBy.value ==
                                'base_image',
                          ),
                        ];
                      },
                      buttonBuilder: (context, showMenu) {
                        return CupertinoButton(
                          onPressed: showMenu,
                          padding: EdgeInsets.zero,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                settingController.overlayAutoClassifyBy.value ==
                                        'watermark'
                                    ? '水印'
                                    : '底图',
                                style: TextStyle(
                                  color: CupertinoColors.secondaryLabel
                                      .resolveFrom(context),
                                ),
                              ),
                              const SizedBox(width: 4),
                              SFIcon(
                                SFIcons.sf_chevron_up_chevron_down,
                                color: CupertinoColors.secondaryLabel
                                    .resolveFrom(context),
                                fontSize: 14,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
              ];

              return CupertinoFormSection.insetGrouped(
                backgroundColor: CupertinoColors.systemGrey6.resolveFrom(
                  context,
                ),
                header: Padding(padding: const EdgeInsets.only(left: 20)),
                footer: Padding(
                  padding: const EdgeInsets.only(left: 20),
                  child: Text(
                    "输出图片将会保存到不同的分类文件夹内。",
                    style: TextStyle(
                      color: CupertinoColors.secondaryLabel.resolveFrom(
                        context,
                      ),
                    ),
                  ),
                ),
                children:
                    formChildren.asMap().entries.map((entry) {
                      final int index = entry.key;
                      final Widget child = entry.value;

                      return Container(
                        decoration: BoxDecoration(
                          color: MyBgColor.sheetFormRowBgColor().resolveFrom(
                            context,
                          ),
                          borderRadius: _getBorderRadius(
                            index,
                            formChildren.length,
                          ),
                        ),
                        child: child,
                      );
                    }).toList(),
              );
            }),

            CupertinoFormSection.insetGrouped(
              backgroundColor: CupertinoColors.systemGrey6.resolveFrom(context),
              header: Padding(padding: const EdgeInsets.only(left: 20)),
              footer: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: Text(
                  "输出图片将会在系统相册中显示。",
                  style: TextStyle(
                    color: CupertinoColors.secondaryLabel.resolveFrom(context),
                  ),
                ),
              ),
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: MyBgColor.sheetFormRowBgColor().resolveFrom(context),
                    borderRadius: _getBorderRadius(0, 1), // 只有一个元素，使用四个圆角
                  ),
                  child: CupertinoFormRow(
                    prefix: Text("输出保存到相册"),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 2.5,
                    ),
                    child: Obx(
                      () => CupertinoSwitch(
                        value: settingController.overlayOutputToAlbum.value,
                        onChanged: (value) {
                          settingController.overlayOutputToAlbum.value = value;
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
