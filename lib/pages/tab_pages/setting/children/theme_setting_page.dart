import 'package:flutter/cupertino.dart';
import 'package:flutter_sficon/flutter_sficon.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/hive/setting_box.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';

class ThemeSettingPage extends GetView<SettingController> {
  const ThemeSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      child: SuperScaffold(
        appBar: SuperAppBar(
          height: 44,
          previousPageTitle: "设置",
          largeTitle: SuperLargeTitle(enabled: false),
          title: Text("外观"),
          searchBar: SuperSearchBar(enabled: false),
          backgroundColor: MyBgColor.settingAppBarBgColor(context),
        ),
        body: ListView(
          padding: EdgeInsets.zero,
          children: [
            const SizedBox(height: 20),
            CupertinoFormSection.insetGrouped(
              // header: Padding(
              //   padding: const EdgeInsets.only(left: 20),
              //   // child: Text("外观"),
              // ),
              children: [
                // 自动（跟随系统）选项
                _buildThemeOption(
                  context,
                  title: '自动',
                  subtitle: '跟随系统设置',
                  mode: BrightnessMode.auto,
                ),
                // 浅色模式选项
                _buildThemeOption(
                  context,
                  title: '浅色',
                  mode: BrightnessMode.light,
                ),
                // 深色模式选项
                _buildThemeOption(
                  context,
                  title: '深色',
                  mode: BrightnessMode.dark,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建主题选项行
  Widget _buildThemeOption(
    BuildContext context, {
    required String title,
    String? subtitle,
    required BrightnessMode mode,
  }) {
    return Obx(() {
      final isSelected = controller.brightnessMode.value == mode;

      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => _selectThemeMode(mode),
        child: CupertinoFormRow(
          padding: const EdgeInsets.fromLTRB(20, 12, 20, 12),
          prefix: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title),
              if (subtitle != null)
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: CupertinoColors.secondaryLabel.resolveFrom(context),
                  ),
                ),
            ],
          ),
          child:
              isSelected
                  ? SFIcon(
                    SFIcons.sf_checkmark,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: CupertinoTheme.of(context).primaryColor,
                  )
                  : const SizedBox(width: 20),
        ),
      );
    });
  }

  // 选择主题模式
  void _selectThemeMode(BrightnessMode mode) {
    controller.updateBrightnessMode(mode);
    // 返回上一页
    // Get.back();
  }
}
