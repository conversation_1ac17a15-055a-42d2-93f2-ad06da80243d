import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/hive/setting_box.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/pages/tab_pages/setting/children/theme_setting_page.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';

class SettingPage extends GetView<SettingController> {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      child: SuperScaffold(
        appBar: SuperAppBar(
          height: 44,
          largeTitle: SuperLargeTitle(largeTitle: "设置"),
          searchBar: SuperSearchBar(enabled: false),
          backgroundColor: MyBgColor.settingAppBarBgColor(context),
        ),
        body: ListView(
          padding: EdgeInsets.zero,
          children: [
            CupertinoFormSection.insetGrouped(
              header: Padding(padding: const EdgeInsets.only(left: 20)),
              children: [
                // 个人资料
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    // 点击后的操作，例如跳转到登录页面
                    // Get.to(() => LoginPage());
                  },
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(20, 12, 16, 12),
                    child: Row(
                      children: [
                        // 左侧圆形图标 - 使用渐变蓝色背景，更接近iOS设置样式
                        Container(
                          width: 58,
                          height: 58,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                CupertinoColors.systemBlue.withOpacity(0.7),
                                CupertinoColors.activeBlue,
                              ],
                              center: Alignment.topLeft,
                              radius: 1.0,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: CupertinoColors.systemBlue.withOpacity(
                                  0.2,
                                ),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Center(
                            child: Icon(
                              CupertinoIcons.person_fill,
                              color: CupertinoColors.white,
                              size: 28,
                            ),
                          ),
                        ),
                        const SizedBox(width: 15),
                        // 中间文本
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "七海账户",
                                style: TextStyle(
                                  fontSize: 17,
                                  fontWeight: FontWeight.w600,
                                  color: CupertinoColors.label.resolveFrom(
                                    context,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                "登录以访问 Premium 服务",
                                style: TextStyle(
                                  fontSize: 13,
                                  color: CupertinoColors.secondaryLabel
                                      .resolveFrom(context),
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        // 右侧箭头
                        Icon(
                          CupertinoIcons.chevron_right,
                          size: 16,
                          color: CupertinoColors.systemGrey.resolveFrom(
                            context,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // 外观
            CupertinoFormSection.insetGrouped(
              header: Padding(padding: const EdgeInsets.only(left: 20)),
              children: <Widget>[
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => Get.to(() => const ThemeSettingPage()),
                  child: CupertinoFormRow(
                    padding: const EdgeInsets.fromLTRB(20, 6, 16, 6),
                    prefix: const PrefixWidget(
                      icon: CupertinoIcons.paintbrush,
                      title: '外观',
                      color: CupertinoColors.systemBlue,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        Obx(() {
                          String themeName;
                          switch (controller.brightnessMode.value) {
                            case BrightnessMode.auto:
                              themeName = '自动';
                              break;
                            case BrightnessMode.light:
                              themeName = '浅色';
                              break;
                            case BrightnessMode.dark:
                              themeName = '深色';
                              break;
                          }
                          return Text(
                            themeName,
                            style: TextStyle(
                              color: CupertinoColors.secondaryLabel
                                  .resolveFrom(context)
                                  .resolveFrom(context),
                            ),
                          );
                        }),
                        const SizedBox(width: 5),
                        Icon(
                          CupertinoIcons.forward,
                          size: 18,
                          color: CupertinoColors.tertiaryLabel.resolveFrom(
                            context,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // 信息
            CupertinoFormSection.insetGrouped(
              header: Padding(padding: const EdgeInsets.only(left: 20)),
              children: <Widget>[
                CupertinoFormRow(
                  padding: const EdgeInsets.fromLTRB(20, 6, 20, 6),
                  prefix: const PrefixWidget(
                    icon: CupertinoIcons.info,
                    title: '版本信息',
                    color: CupertinoColors.systemGrey,
                  ),
                  child: Obx(
                    () =>
                        controller.isLoading.value
                            ? const CupertinoActivityIndicator()
                            : Text(
                              controller.version.value,
                              style: TextStyle(
                                color: CupertinoColors.secondaryLabel
                                    .resolveFrom(context),
                              ),
                            ),
                  ),
                ),
                CupertinoFormRow(
                  padding: const EdgeInsets.fromLTRB(20, 6, 20, 6),
                  prefix: const PrefixWidget(
                    icon: CupertinoIcons.money_dollar_circle,
                    title: '订阅方案',
                    color: CupertinoColors.systemOrange,
                  ),
                  child: Text(
                    'Free',
                    style: TextStyle(
                      color: CupertinoColors.secondaryLabel.resolveFrom(
                        context,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class PrefixWidget extends StatelessWidget {
  const PrefixWidget({
    super.key,
    required this.icon,
    required this.title,
    required this.color,
  });

  final IconData icon;
  final String title;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Container(
          padding: const EdgeInsets.all(4.0),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: Icon(icon, color: CupertinoColors.white),
        ),
        const SizedBox(width: 15),
        Text(
          title,
          style: TextStyle(color: CupertinoColors.label.resolveFrom(context)),
        ),
      ],
    );
  }
}
