import 'dart:io';
import 'dart:ui';

import 'package:drag_select_grid_view/drag_select_grid_view.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:glass/glass.dart';
import 'package:nanami_flutter/common/controllers/my_images_controller.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/widgets/cupertino_sheet_top_bar.dart';
import 'package:smooth_sheets/smooth_sheets.dart';

/// 显示多选图片的 Sheet，返回选中的图片 ID 数组或 null
Future<List<int>?> showSelectMultipleImagesSheet(
  BuildContext context, {
  String? title,
}) async {
  var result = await Navigator.push<List<int>?>(
    context,
    CupertinoModalSheetRoute(
      // 启用滑动关闭功能，用户可以通过向下滑动来关闭sheet
      swipeDismissible: true,
      builder: (context) {
        return SelectMultipleImagesSheet(title: title ?? "选择项目");
      },
    ),
  );

  // 获取控制器
  final MyImagesController controller = Get.find<MyImagesController>();

  // 200 毫秒后，清空搜索结果
  // 使用异步操作确保在页面关闭后执行
  await Future.delayed(Duration(milliseconds: 200));
  await controller.clearSearch();

  LoggerUtil.logger.d("选择图片结果: $result");
  return result;
}

class SelectMultipleImagesSheet extends StatefulWidget {
  // Sheet 标题
  final String title;

  const SelectMultipleImagesSheet({super.key, this.title = "选择项目"});

  @override
  State<SelectMultipleImagesSheet> createState() =>
      _SelectMultipleImagesSheetState();
}

class _SelectMultipleImagesSheetState extends State<SelectMultipleImagesSheet> {
  // 获取控制器
  final MyImagesController controller = Get.find<MyImagesController>();

  // 创建一个焦点节点用于控制搜索框的焦点
  final FocusNode _searchFocusNode = FocusNode();

  // 创建一个文本控制器用于搜索框
  final TextEditingController _searchController = TextEditingController();

  // 创建一个选择控制器
  final selectController = DragSelectGridViewController();

  // 已选中的图片ID列表
  final RxList<int> selectedImageIds = <int>[].obs;

  @override
  void initState() {
    super.initState();
    // 添加选择控制器监听
    selectController.addListener(_updateSelectedImages);
  }

  // 更新选中的图片ID列表
  void _updateSelectedImages() {
    // 通过索引获取图片ID
    final newSelectedIds =
        selectController.value.selectedIndexes
            .map(
              (index) =>
                  index < controller.images.length
                      ? controller.images[index].id
                      : -1,
            )
            .where((id) => id != -1)
            .toList();

    // 只有当选择发生变化时才更新状态
    if (selectedImageIds.length != newSelectedIds.length ||
        !selectedImageIds.toSet().containsAll(newSelectedIds.toSet())) {
      selectedImageIds.value = newSelectedIds;
    }
  }

  // 处理搜索框内容变化
  void _handleSearchChanged(String query) {
    // 更新搜索状态
    controller.isSearching.value = query.isNotEmpty;

    // 如果搜索框为空，清除搜索结果
    if (query.isEmpty) {
      controller.clearSearch();
    }
  }

  // 处理搜索提交
  Future<void> _handleSearchSubmitted(String query) async {
    // 执行搜索
    await controller.searchImages(query);
    // 搜索完成后取消焦点
    _searchFocusNode.unfocus();
  }

  // 处理清除搜索
  void _handleClearSearch() {
    // 清空搜索框
    _searchController.clear();
    // 清除搜索
    controller.clearSearch();
    // 取消焦点
    _searchFocusNode.unfocus();
  }

  @override
  void dispose() {
    // 释放焦点节点
    _searchFocusNode.dispose();
    // 释放文本控制器
    _searchController.dispose();
    // 移除选择控制器监听
    selectController.removeListener(_updateSelectedImages);
    // 释放选择控制器
    selectController.dispose();
    super.dispose();
  }

  // 构建内容部分
  Widget _buildContent(BuildContext context, double bottomPadding) {
    // 如果正在加载，显示加载状态
    if (controller.isLoading.value) {
      return const Center(child: CupertinoActivityIndicator(radius: 15));
    }

    // 如果没有图片，显示空状态
    if (controller.images.isEmpty) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              CupertinoIcons.photo,
              size: 64,
              color: MyBgColor.emptyPageIconColor(context).resolveFrom(context),
            ),
            const SizedBox(height: 12),
            // 搜索结果未找到的提示
            controller.isSearching.value
                ? Text(
                  "未找到相关图片",
                  style: TextStyle(
                    fontSize: 18,
                    color: CupertinoColors.systemGrey.resolveFrom(context),
                  ),
                )
                : Column(
                  children: [
                    Text(
                      "无图片",
                      style: TextStyle(
                        fontSize: 21,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "已添加的图片将在此处显示。",
                      style: TextStyle(
                        fontSize: 15,
                        color: CupertinoColors.secondaryLabel.resolveFrom(
                          context,
                        ),
                      ),
                    ),
                  ],
                ),
          ],
        ),
      );
    }

    // 使用 DragSelectGridView 实现多选功能
    return DragSelectGridView(
      // 顶部留出足够空间给搜索框
      padding: EdgeInsets.only(top: 0, bottom: bottomPadding),
      itemCount: controller.images.length,
      triggerSelectionOnTap: true,
      gridController: selectController,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        mainAxisSpacing: 1,
        crossAxisSpacing: 1,
      ),
      itemBuilder: (context, index, selected) {
        final imageInfo = controller.images[index];
        // 多选模式下的图片项
        return Stack(
          fit: StackFit.expand,
          children: [
            // 图片
            ExtendedImage.file(
              File(imageInfo.thumbnailPath),
              fit: BoxFit.cover,
            ),
            // 选中状态的半透明白色蒙版
            if (selected)
              Container(color: const Color.fromRGBO(255, 255, 255, 0.3)),
            // 右下角的选中图标
            if (selected)
              Positioned(
                right: 4,
                bottom: 4,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: CupertinoColors.activeBlue,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: CupertinoColors.white,
                      width: 1.3,
                    ),
                  ),
                  child: const Icon(
                    CupertinoIcons.checkmark,
                    color: CupertinoColors.white,
                    size: 12,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SheetKeyboardDismissible(
      dismissBehavior: const SheetKeyboardDismissBehavior.onDragDown(),
      child: Sheet(
        // Sheet 装饰器
        decoration: SheetDecorationBuilder(
          size: SheetSize.stretch,
          builder: (context, child) {
            return ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: ColoredBox(
                color: CupertinoColors.secondarySystemGroupedBackground
                    .resolveFrom(context),
                child: child,
              ),
            );
          },
        ),
        child: SheetContentScaffold(
          backgroundColor: CupertinoColors.secondarySystemGroupedBackground
              .resolveFrom(context),
          topBar: CupertinoSheetTopBar(
            backgroundColor: Colors.transparent,
            // 使用Obx监听selectedImageIds变化，更新标题
            title: Obx(
              () => Text(
                selectedImageIds.isEmpty
                    ? widget.title
                    : "已选择 ${selectedImageIds.length} 张",
              ),
            ),
            leading: CupertinoButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            // 使用Obx监听selectedImageIds变化，更新完成按钮状态
            trailing: Obx(
              () => CupertinoButton(
                // 当没有选择任何图片时禁用按钮
                onPressed:
                    selectedImageIds.isEmpty
                        ? null
                        : () => Navigator.pop(context, selectedImageIds),
                child: const Text(
                  '添加',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ),
            ),
          ),
          body: Stack(
            children: [
              // 使用Obx监听controller.isLoading和controller.images变化
              Obx(() {
                // 获取媒体查询的底部内边距
                final bottomPadding = MediaQuery.of(context).padding.bottom;

                // 构建内容部分
                return _buildContent(context, bottomPadding);
              }),
              // 搜索栏叠加在gridview上方
              Positioned(
                top: 8,
                left: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Container(
                    decoration: BoxDecoration(
                      // 添加外阴影
                      boxShadow: [
                        BoxShadow(
                          color: CupertinoDynamicColor.resolve(
                            CupertinoColors.black,
                            context,
                          ).withAlpha(26), // 0.1 * 255 ≈ 26
                          blurRadius: 8.0,
                          spreadRadius: 0.0,
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      // 超椭圆平滑圆角
                      shape: RoundedSuperellipseBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: BackdropFilter(
                        filter: ImageFilter.blur(
                          sigmaX: 12.0, // Vision Pro 风格的模糊参数
                          sigmaY: 12.0, // Vision Pro 风格的模糊参数
                          tileMode: TileMode.clamp,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            color: CupertinoColors.white.withAlpha(
                              51,
                            ), // Vision Pro 风格的背景透明度 (0.2 * 255 ≈ 51)
                            border: Border.all(
                              color: CupertinoDynamicColor.resolve(
                                CupertinoColors.white,
                                context,
                              ).withAlpha(
                                51,
                              ), // Vision Pro 风格的边框 (0.2 * 255 ≈ 51)
                              width: 0.8, // 保持细边框
                            ),
                            borderRadius: BorderRadius.circular(
                              12.0,
                            ), // Vision Pro 风格更圆润的边角
                          ),
                          child: CupertinoSearchTextField(
                            placeholder: "搜索",
                            backgroundColor:
                                Colors.transparent, // 使用透明背景，让上层的玻璃效果显示
                            focusNode: _searchFocusNode,
                            controller: _searchController,
                            onChanged: _handleSearchChanged,
                            onSubmitted: _handleSearchSubmitted,
                            onSuffixTap: _handleClearSearch,
                            placeholderStyle: TextStyle(
                              color: CupertinoColors.white,
                            ),
                            suffixIcon: const Icon(
                              CupertinoIcons.xmark_circle_fill,
                              color: CupertinoColors.white,
                            ),
                            prefixIcon: Icon(
                              CupertinoIcons.search,
                              color: CupertinoColors.white,
                            ),
                            style: TextStyle(color: CupertinoColors.white),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
