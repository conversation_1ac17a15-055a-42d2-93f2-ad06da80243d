import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:nanami_flutter/common/controllers/add_image_controller.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/services/image.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/common/utils/human_face_detect.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/widgets/bottom_actions.dart';
import 'package:nanami_flutter/widgets/face_detect_setting_sheet.dart';
import 'package:nanami_flutter/widgets/github_action_item.dart';
import 'package:nanami_flutter/widgets/image_tile_item.dart';
import 'package:nanami_flutter/widgets/pending_images_preview.dart';
import 'package:nanami_flutter/widgets/show_mask_setting_sheet.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';
import 'package:toastification/toastification.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:flutter/material.dart';
import 'package:loader_overlay/loader_overlay.dart';

class AddImagePage extends StatefulWidget {
  const AddImagePage({super.key});

  @override
  State<AddImagePage> createState() => _AddImagePageState();
}

class _AddImagePageState extends State<AddImagePage>
    with SingleTickerProviderStateMixin {
  // 获取 Controller
  final AddImageController controller = Get.find<AddImageController>();

  // 设置 Controller
  final SettingController _settingController = Get.find<SettingController>();

  // logger
  final logger = LoggerUtil.logger;

  // 批量识别状态
  final RxBool isBatchDetecting = false.obs;

  // 批量识别进度
  final RxInt batchDetectProgress = 0.obs;

  // 批量识别百分比进度
  final RxDouble batchDetectProgressPercent = 0.0.obs;

  // 批量识别成功数量
  final RxInt batchDetectSuccessCount = 0.obs;

  // 批量识别失败数量
  final RxInt batchDetectFailCount = 0.obs;

  // 模糊效果动画控制器
  late AnimationController _blurAnimationController;
  late Animation<double> _blurAnimation;
  // 添加不透明度动画
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initBlurAnimation();
  }

  @override
  void dispose() {
    _blurAnimationController.dispose();
    super.dispose();
  }

  // 初始化模糊动画控制器
  void _initBlurAnimation() {
    _blurAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 350),
    );
    _blurAnimation = Tween<double>(begin: 0.0, end: 8.0).animate(
      CurvedAnimation(
        parent: _blurAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    // 添加不透明度动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _blurAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LoaderOverlay(
      // 加载时显示的组件
      overlayColor: CupertinoColors.systemGroupedBackground
          .resolveFrom(context)
          .withValues(alpha: 0.4),
      useDefaultLoading: false,
      overlayWidgetBuilder: (process) {
        return _overlayWidgetBuilder(context, process);
      },
      child: CupertinoPageScaffold(
        backgroundColor: CupertinoColors.systemBackground,
        child: SuperScaffold(
          // 导航栏组件
          appBar: SuperAppBar(
            backgroundColor: MyBgColor.appBarBgColor(context),
            height: 44,
            previousPageTitle: "图库",
            largeTitle: SuperLargeTitle(enabled: false),
            searchBar: SuperSearchBar(enabled: false),
            title: Text("添加图片"),
            // 导航栏右侧按钮
            actions: Padding(
              padding: const EdgeInsets.only(right: 6.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      _showPreviewSettings(context);
                    },
                    child: const Icon(CupertinoIcons.eye, size: 24.0),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      _addImages(context);
                    },
                    child: const Icon(CupertinoIcons.add, size: 24.0),
                  ),
                  Obx(
                    () => CupertinoButton(
                      padding: EdgeInsets.zero,
                      onPressed:
                          controller.pendingImages.isEmpty
                              ? null
                              : () {
                                _completeAddImages(context);
                              },
                      child: const Icon(
                        CupertinoIcons.checkmark_alt,
                        size: 24.0,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 导航栏底部
            bottom: SuperAppBarBottom(
              enabled: true,
              height: 55,
              child: BottomActions(
                actions: [
                  GithubActionItem(
                    icon: CupertinoIcons.viewfinder,
                    text: '真人',
                    onPressed: () {
                      _detectAllHumanFaces(context);
                    },
                  ),
                  GithubActionItem(
                    icon: CupertinoIcons.viewfinder,
                    text: '动漫',
                    onPressed: () {
                      logger.d('从相册选择');
                    },
                  ),
                  Obx(
                    () => GithubActionItem(
                      text:
                          '${_settingController.faceDetectExpansionRate.value} %',
                      onPressed: () {
                        // 弹出范围扩展倍率选择 sheet
                        showFaceDetectSettingSheet(context);
                      },
                      showDropdownArrow: true,
                    ),
                  ),
                  GithubActionItem(
                    icon: CupertinoIcons.delete,
                    iconColor: CupertinoColors.destructiveRed,
                    textColor: CupertinoColors.destructiveRed,
                    text: '清空',
                    onPressed: () {
                      _showClearConfirmDialog(context);
                    },
                  ),
                ],
              ),
            ),
          ),
          // 主体
          body: Obx(() {
            // 有图片的时候，渲染列表
            if (controller.pendingImages.isNotEmpty) {
              return _buildPendinImagesList(context);
              // 空图片提示
            } else {
              return Center(
                child: Text(
                  '未选择图片',
                  style: TextStyle(
                    color: CupertinoColors.tertiaryLabel.resolveFrom(context),
                  ),
                ),
              );
            }
          }),
          // 未选择图片时的提示
        ),
      ),
    );
  }

  // 构建加载效果
  Widget _overlayWidgetBuilder(BuildContext context, dynamic process) {
    // 启动模糊动画
    _blurAnimationController.forward();

    return AnimatedBuilder(
      animation: _blurAnimationController,
      builder: (context, child) {
        return BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: _blurAnimation.value,
            sigmaY: _blurAnimation.value,
            tileMode: TileMode.clamp,
          ),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Lottie 动画
                  Lottie.asset(
                    "assets/lotties/face_detecing.json",
                    width: 200,
                    height: 200,
                    frameRate: FrameRate.max,
                  ),
                  // const SizedBox(height: 16),
                  Text(
                    "正在识别脸部",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: CupertinoColors.systemBlue.resolveFrom(context),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // 百分比
                  Obx(
                    () => Text(
                      "${batchDetectProgressPercent.value.toStringAsFixed(0)} %",
                      style: TextStyle(
                        fontSize: 14,
                        color: CupertinoColors.secondaryLabel.resolveFrom(
                          context,
                        ),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // 构建图片列表
  Widget _buildPendinImagesList(BuildContext context) {
    // 获取媒体查询的底部内边距
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    return Obx(() {
      return Stack(
        children: [
          AnimatedList(
            key: controller.listKey,
            // 设置 padding 底部
            padding: EdgeInsets.only(bottom: bottomPadding),
            initialItemCount: controller.pendingImages.length,
            itemBuilder: (context, index, animation) {
              // 获取 pendingImage 对象
              PendingImage pendingImage = controller.pendingImages[index];
              return _buildAnimatedItem(
                pendingImage,
                context,
                animation,
                index,
              );
            },
          ),
        ],
      );
    });
  }

  // 构建图片列表项（带有动画）
  Widget _buildAnimatedItem(
    PendingImage pendingImage,
    BuildContext context,
    Animation<double> animation,
    int index,
  ) {
    // 使用FadeTransition和SizeTransition组合动画效果
    return SizeTransition(
      sizeFactor: animation,
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            // 使用Obx包装ImageTileItem，以便在pendingImage.name变化时自动刷新UI
            Obx(() {
              return ImageTileItem(
                key: ValueKey(pendingImage.filePath), // 使用文件路径作为唯一key
                title: pendingImage.name,
                imagePath: pendingImage.filePath,
                maskPath: pendingImage.maskFilePath,
                maskBlendMode: _settingController.showMaskBlendMode.value,
                maskOpacity: _settingController.showMaskOpacity.value,
                // 图片点击进入预览页
                onImageTap: () {
                  Get.to(
                    () => PendingImagesPreview(
                      initIndex: index,
                      pendingImages: controller.pendingImages,
                    ),
                  );
                },
                // 触发删除
                onDelete: () {
                  // 调用控制器的移除方法
                  controller.removePendingImageByIndex(index);
                },
                // 长按标题触发重命名
                onTitleLongPressed: () {
                  // 振动反馈
                  HapticFeedback.vibrate();
                  // 弹出重命名模态框
                  _showRenameDialog(context, pendingImage);
                },
                // 识别真人脸部
                onDetectHumanFace: () {
                  _detectHumanFace(context, pendingImage);
                },
              );
            }),
            // 分割线
            Divider(
              height: 1,
              color: CupertinoColors.systemGroupedBackground.resolveFrom(
                context,
              ),
              indent: 97.0,
              endIndent: 15.0,
            ),
          ],
        ),
      ),
    );
  }

  // 显示清空确认对话框
  void _showClearConfirmDialog(BuildContext context) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CupertinoAlertDialog(
          title: const Text('清空图片列表？'),
          content: Text("将会移除该页面下所有的图片。"),
          actions: [
            CupertinoDialogAction(
              child: const Text('取消'),
              onPressed: () {
                Navigator.pop(dialogContext);
              },
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              child: const Text('清空'),
              onPressed: () {
                // 调用控制器的清空方法
                controller.clearPendingImages();
                Navigator.pop(dialogContext);
              },
            ),
          ],
        );
      },
    );
  }

  // 点击右上角添加按钮
  void _addImages(context) async {
    final List<AssetEntity>? assetEntities = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        selectedAssets: controller.assetEntites,
        maxAssets: 99,
        requestType: RequestType.image,
        // themeColor: CupertinoTheme.of(context).primaryColor,
        pathNameBuilder: (AssetPathEntity path) {
          // 处理特殊相册名称
          if (path.isAll) {
            return '所有图片';
          }
          if (path.name == 'Camera') {
            return '相机';
          }
          if (path.name == 'Screenshots') {
            return '截屏';
          }
          if (path.name == 'Download') {
            return '下载';
          }
          if (path.name == ('WeChat') || path.name == ('WeiXin')) {
            return '微信';
          }
          if (path.name == ('douyin')) {
            return '抖音';
          }
          if (path.name == ('bili')) {
            return '哔哩哔哩';
          }
          // 默认返回原始名称
          return path.name;
        },
      ),
    );

    // 将获取到的资产添加进图片列表
    if (assetEntities != null) {
      for (var asset in assetEntities) {
        // 如果 asset 不存在与 controller.assetEntites 中，则添加
        if (!controller.assetEntites.contains(asset)) {
          controller.addPendingImage(asset);
        }
      }
    }
  }

  // 长按图片时弹出重命名模态框
  void _showRenameDialog(BuildContext context, PendingImage pendingImage) {
    // 创建一个文本控制器，初始值为空字符串
    final TextEditingController textController = TextEditingController();

    // 创建一个焦点节点，用于控制键盘焦点
    final FocusNode focusNode = FocusNode();

    // 显示中间模态对话框
    showCupertinoDialog<void>(
      context: context,
      barrierDismissible: true, // 点击背景可关闭对话框
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('重命名图片'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 当前名称显示
              Padding(
                padding: const EdgeInsets.only(top: 8.0, bottom: 12.0),
                child: Text(
                  pendingImage.name,
                  style: TextStyle(
                    color: CupertinoColors.secondaryLabel.resolveFrom(context),
                    fontSize: 13.0,
                  ),
                ),
              ),
              // 输入框
              CupertinoTextField(
                controller: textController,
                focusNode: focusNode,
                placeholder: '输入新名称',
                clearButtonMode: OverlayVisibilityMode.editing,
                decoration: BoxDecoration(
                  color: CupertinoColors.systemFill.resolveFrom(context),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 8.0,
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            // 取消按钮
            CupertinoDialogAction(
              isDefaultAction: true,
              child: const Text('取消'),
              onPressed: () {
                // 首先收起键盘
                focusNode.unfocus();
                // 然后关闭对话框
                Navigator.of(context).pop();
              },
            ),
            // 确认按钮
            CupertinoDialogAction(
              child: const Text('确认'),
              onPressed: () {
                // 首先收起键盘
                focusNode.unfocus();

                // 获取新名称
                final newName = textController.text.trim();
                // 检查名称是否为空
                if (newName.isEmpty) {
                  // 显示错误提示
                  showCupertinoDialog(
                    context: context,
                    builder:
                        (context) => CupertinoAlertDialog(
                          title: const Text('错误'),
                          content: const Text('名称不能为空'),
                          actions: [
                            CupertinoDialogAction(
                              child: const Text('确定'),
                              onPressed: () => Navigator.pop(context),
                            ),
                          ],
                        ),
                  );
                  return;
                }

                // 更新图片名称
                pendingImage.name = newName;

                // 关闭对话框
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    ).then((_) {
      // 释放资源
      textController.dispose();
      focusNode.dispose();
    });
  }

  // 显示预览设置
  void _showPreviewSettings(BuildContext context) {
    showShowMaskSettingSheet(context);
  }

  // 识别真人脸部(单张图片)
  void _detectHumanFace(BuildContext context, PendingImage pendingImage) async {
    _blurAnimationController.reset();

    // final toast = toastification.show(
    //   type: ToastificationType.info,
    //   style: ToastificationStyle.flat,
    //   title: Text("正在识别脸部"),
    //   description: Text("请耐心等待..."),
    //   alignment: Alignment.topCenter,
    //   autoCloseDuration: const Duration(seconds: 4),
    //   closeButton: ToastCloseButton(showType: CloseButtonShowType.none),
    //   closeOnClick: false,
    //   pauseOnHover: false,
    // );

    try {
      // 调用优化后的人脸检测方法（现在在独立Isolate中执行）
      String? path = await HumanFaceDetectUtils.getAutoFaceContoursMask(
        pendingImage.filePath,
        expansionRate: _settingController.faceDetectExpansionRate.value,
      );

      // 关闭加载提示
      // toastification.dismiss(toast);

      // 只有在成功识别到人脸时才设置蒙版路径
      if (path != null) {
        pendingImage.maskFilePath = path;
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          icon: Lottie.asset(
            "assets/lotties/face_detect_successful.json",
            frameRate: FrameRate.max,
            width: 50,
            height: 50,
            repeat: false,
            delegates: LottieDelegates(
              values: [
                // 使用 ColorFilter 修改所有颜色
                ValueDelegate.colorFilter(
                  const ['**'], // 匹配所有图层
                  value: ColorFilter.mode(Colors.green, BlendMode.srcIn),
                ),
              ],
            ),
          ),
          title: Text("识别成功"),
          description: Text("已标注脸部"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
      } else {
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          icon: Lottie.asset(
            "assets/lotties/face_detect_failed.json",
            frameRate: FrameRate.max,
            width: 50,
            height: 50,
            repeat: false,
            delegates: LottieDelegates(
              values: [
                // 使用 ColorFilter 修改所有颜色
                ValueDelegate.colorFilter(
                  const ['**'], // 匹配所有图层
                  value: ColorFilter.mode(Colors.orangeAccent, BlendMode.srcIn),
                ),
              ],
            ),
          ),
          title: Text("识别失败"),
          description: Text("未检测到人脸"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 2),
          dragToClose: true,
        );
      }
    } catch (e) {
      // 关闭加载提示
      // toastification.dismiss(toast);
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("识别失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
    }
  }

  // 批量识别所有图片中的人脸
  void _detectAllHumanFaces(BuildContext context) async {
    // 如果没有图片，直接返回
    if (controller.pendingImages.isEmpty) {
      toastification.show(
        type: ToastificationType.warning,
        style: ToastificationStyle.flat,
        title: Text("未选择图片"),
        description: Text("请先添加图片"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 2),
        dragToClose: true,
      );
      return;
    }

    // 如果正在进行批量识别，直接返回
    if (isBatchDetecting.value) {
      return;
    }

    // 重置状态
    batchDetectProgress.value = 0;
    batchDetectSuccessCount.value = 0;
    batchDetectFailCount.value = 0;
    batchDetectProgressPercent.value = 0.0;

    // 重置动画控制器
    _blurAnimationController.reset();

    // 设置为正在识别状态
    isBatchDetecting.value = true;
    context.loaderOverlay.show();

    try {
      // 获取图片总数
      final int totalCount = controller.pendingImages.length;

      // 遍历所有图片，逐个进行人脸识别
      for (int i = 0; i < totalCount; i++) {
        final PendingImage pendingImage = controller.pendingImages[i];

        // 调用优化后的人脸检测方法
        String? path = await HumanFaceDetectUtils.getAutoFaceContoursMask(
          pendingImage.filePath,
          expansionRate: _settingController.faceDetectExpansionRate.value,
        );

        // 更新进度
        batchDetectProgress.value = i + 1;
        // 计算百分比进度
        batchDetectProgressPercent.value = ((i + 1) / totalCount) * 100;

        // 根据识别结果更新计数
        if (path != null) {
          // 识别成功
          pendingImage.maskFilePath = path;
          batchDetectSuccessCount.value++;
        } else {
          // 识别失败
          batchDetectFailCount.value++;
        }
      }

      // 根据识别结果显示不同类型的 toast
      if (batchDetectFailCount.value == 0) {
        // 全部识别成功
        toastification.show(
          type: ToastificationType.success,
          style: ToastificationStyle.flat,
          title: Text("全部识别成功"),
          description: Text("共识别了 ${batchDetectSuccessCount.value} 张图片"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      } else {
        // 部分识别失败
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          title: Text("部分识别成功"),
          description: Text(
            "成功 ${batchDetectSuccessCount.value} 张，未识别到 ${batchDetectFailCount.value} 张",
          ),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      }
    } catch (e) {
      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("识别失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 3),
        dragToClose: true,
      );
    } finally {
      // 无论成功还是失败，都将状态设置为非识别状态
      isBatchDetecting.value = false;

      // 添加mounted检查，确保组件仍然挂载
      if (context.mounted) {
        // 反向运行模糊动画
        _blurAnimationController.reverse().then((_) {
          context.loaderOverlay.hide();
        });
      }
    }
  }

  // 完成添加图片
  void _completeAddImages(BuildContext context) async {
    // 如果没有图片，直接返回
    if (controller.pendingImages.isEmpty) {
      // 弹出 cupertino 提示
      showCupertinoDialog(
        context: context,
        builder:
            (context) => CupertinoAlertDialog(
              title: const Text('添加失败'),
              content: const Text('未选择任何图片。'),
              actions: [
                CupertinoDialogAction(
                  child: const Text('好'),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
      );
      return;
    }

    // 显示加载提示
    final toast = toastification.show(
      type: ToastificationType.info,
      style: ToastificationStyle.flat,
      title: Text("正在保存图片"),
      description: Text("请耐心等待..."),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 10),
      closeButton: ToastCloseButton(showType: CloseButtonShowType.none),
      closeOnClick: false,
      pauseOnHover: false,
    );

    try {
      // 保存所有图片
      int successCount = 0;
      int failCount = 0;
      List<String> errorMessages = [];

      // 遍历所有待添加的图片
      for (var pendingImage in controller.pendingImages) {
        try {
          // 调用图片服务的添加方法
          await ImageService.add(pendingImage);
          successCount++;
        } catch (e) {
          failCount++;
          errorMessages.add(e.toString());
          logger.e("添加图片失败: $e");
        }
      }

      // 关闭加载提示
      toastification.dismiss(toast);

      // 检查组件是否仍然挂载
      if (!context.mounted) return;

      // 根据保存结果显示不同类型的 toast
      if (failCount == 0) {
        // 全部保存成功
        toastification.show(
          type: ToastificationType.success,
          style: ToastificationStyle.flat,
          title: Text("保存成功"),
          description: Text("已成功保存 $successCount 张图片"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );

        // 清空待添加列表
        controller.clearPendingImages();

        // 返回上一页，并传递结果表示需要刷新图片列表
        Get.back(result: true);
      } else {
        // 部分保存失败
        toastification.show(
          type: ToastificationType.warning,
          style: ToastificationStyle.flat,
          title: Text("部分保存成功"),
          description: Text("成功 $successCount 张，失败 $failCount 张"),
          alignment: Alignment.topCenter,
          autoCloseDuration: const Duration(seconds: 3),
          dragToClose: true,
        );
      }
    } catch (e) {
      // 关闭加载提示
      toastification.dismiss(toast);

      // 检查组件是否仍然挂载
      if (!context.mounted) return;

      // 显示错误提示
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flat,
        title: Text("保存失败"),
        description: Text("出现错误：$e"),
        alignment: Alignment.topCenter,
        autoCloseDuration: const Duration(seconds: 3),
        dragToClose: true,
      );
    }
  }
}
