import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/binding/add_image_binding.dart';
import 'package:nanami_flutter/common/controllers/my_images_controller.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/theme/color.dart';
import 'package:nanami_flutter/pages/tab_pages/image_group/sheets/select_image_group_sheet.dart';

import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';
import 'package:drag_select_grid_view/drag_select_grid_view.dart';

import 'children/add_image_page.dart';

class MyImagesPage extends StatefulWidget {
  const MyImagesPage({super.key});

  @override
  State<MyImagesPage> createState() => _MyImagesPageState();
}

class _MyImagesPageState extends State<MyImagesPage> {
  // 获取控制器
  final MyImagesController controller = Get.find<MyImagesController>();

  // 创建一个焦点节点用于控制搜索框的焦点
  final FocusNode _searchFocusNode = FocusNode();

  //  创建一个文本控制器用于搜索框
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    // 释放焦点节点
    _searchFocusNode.dispose();
    // 释放文本控制器
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      // backgroundColor: CupertinoColors.systemBackground,
      child: SuperScaffold(
        transitionBetweenRoutes: true,
        appBar: SuperAppBar(
          height: 44,
          backgroundColor: MyBgColor.appBarBgColor(context),
          searchBar: SuperSearchBar(
            placeholderText: "搜索",
            cancelButtonText: "取消",
            scrollBehavior: SearchBarScrollBehavior.pinned,
            resultBehavior: SearchBarResultBehavior.neverVisible,
            searchFocusNode: _searchFocusNode, // 使用焦点节点控制搜索框焦点
            searchController: _searchController, // 使用文本控制器
            onChanged: (query) {
              // 当搜索框内容变化时，更新搜索状态
              controller.isSearching.value = query.isNotEmpty;
              // 如果搜索框为空，立即清除搜索结果
              if (query.isEmpty) {
                controller.clearSearch();
              }
              // 如果需要实时搜索，可以取消下面的注释
              // else {
              //   controller.searchImages(query);
              // }
            },
            onSubmitted: (query) async {
              // 当用户提交搜索时，执行搜索
              await controller.searchImages(query);
              // 搜索完成后取消焦点
              _searchFocusNode.unfocus();
            },
            onFocused: (focused) {
              controller.logger.d("聚焦状态：$focused");
              // 如果取消搜索
              if (!focused) {
                // 取消搜索
                controller.clearSearch();
              }
            },
          ),
          largeTitle: SuperLargeTitle(
            largeTitle: "图库",
            enabled: !controller.isMultiSelecting.value,
          ),
          title: Obx(() {
            // 如果正在搜索，显示搜索结果数量
            if (controller.isSearching.value) {
              return Text("找到 ${controller.images.length} 张图片");
            }
            // 否则显示总图片数量
            else {
              return Text("${controller.images.length} 张图片");
            }
          }),
          actions: Padding(
            padding: const EdgeInsets.only(right: 6.0),
            child: Obx(() {
              // 如果正在多选模式，只显示取消按钮
              if (controller.isMultiSelecting.value) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CupertinoButton(
                      padding: EdgeInsets.only(right: 10),
                      child: const Text("取消"),
                      onPressed: () {
                        // 退出多选模式
                        controller.exitMultiSelectMode();
                      },
                    ),
                  ],
                );
              }

              // 正常模式下的按钮
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 排序按钮
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: Icon(
                      controller.isDescending.value
                          ? CupertinoIcons.chevron_down
                          : CupertinoIcons.chevron_up,
                      size: 24.0,
                    ),
                    onPressed: () async {
                      // 点击后切换排序方式
                      await controller.toggleSortOrder();
                    },
                  ),

                  // 多选按钮
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: const Icon(
                      CupertinoIcons.checkmark_alt_circle,
                      size: 24.0, // 增大图标尺寸
                    ),
                    onPressed: () {
                      // 切换多选模式
                      controller.toggleMultiSelectMode();
                    },
                  ),

                  // 添加按钮
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: const Icon(
                      CupertinoIcons.add,
                      size: 24.0, // 增大图标尺寸
                    ),
                    onPressed: () async {
                      // 导航到添加图片页面，并等待结果
                      final result = await Get.to(
                        () => AddImagePage(),
                        binding: AddImageBinding(),
                      );

                      // 如果返回结果为true，表示添加了图片，需要刷新图片列表
                      if (result == true) {
                        await controller.refreshImages();
                      }
                    },
                  ),
                ],
              );
            }),
          ),
        ),
        body: Obx(() {
          // 获取媒体查询的底部内边距
          final bottomPadding = MediaQuery.of(context).padding.bottom;
          // 如果正在加载，显示加载状态
          if (controller.isLoading.value) {
            return const Center(child: CupertinoActivityIndicator(radius: 15));
          }
          // 如果没有图片，显示空状态
          if (controller.images.isEmpty) {
            return Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    CupertinoIcons.photo,
                    size: 64,
                    color: MyBgColor.emptyPageIconColor(
                      context,
                    ).resolveFrom(context),
                  ),
                  const SizedBox(height: 12),
                  // 搜索结果未找到的提示
                  controller.isSearching.value
                      ? Text(
                        "未找到相关图片",
                        style: TextStyle(
                          fontSize: 18,
                          color: CupertinoColors.systemGrey.resolveFrom(
                            context,
                          ),
                        ),
                      )
                      : Column(
                        children: [
                          Text(
                            "无图片",
                            style: TextStyle(
                              fontSize: 21,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "已添加的图片将在此处显示。",
                            style: TextStyle(
                              fontSize: 15,
                              color: CupertinoColors.secondaryLabel.resolveFrom(
                                context,
                              ),
                            ),
                          ),
                          const SizedBox(height: 44),
                        ],
                      ),
                ],
              ),
            );
          }

          // 使用MediaQuery.removePadding移除顶部内边距
          return MediaQuery.removePadding(
            context: context,
            removeTop: true, // 移除顶部内边距
            child: Obx(() {
              // 定义通用的网格委托
              const gridDelegate = SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                mainAxisSpacing: 1,
                crossAxisSpacing: 1,
              );
              // 多选模式下使用 DragSelectGridView
              if (controller.isMultiSelecting.value) {
                return DragSelectGridView(
                  // 设置padding，顶部为10，底部保留媒体查询的值
                  padding: EdgeInsets.only(top: 8, bottom: bottomPadding),
                  itemCount: controller.images.length,
                  triggerSelectionOnTap: true,
                  gridController: controller.selectController,
                  gridDelegate: gridDelegate,
                  itemBuilder: (context, index, selected) {
                    final imageInfo = controller.images[index];
                    // 多选模式下的图片项
                    return Stack(
                      fit: StackFit.expand,
                      children: [
                        // 图片
                        ExtendedImage.file(
                          File(imageInfo.thumbnailPath),
                          fit: BoxFit.cover,
                        ),
                        // 选中状态的半透明白色蒙版
                        if (selected)
                          Container(
                            color: const Color.fromRGBO(255, 255, 255, 0.3),
                          ),
                        // 右下角的选中图标
                        if (selected)
                          Positioned(
                            right: 4,
                            bottom: 4,
                            child: Container(
                              width: 18,
                              height: 18,
                              decoration: BoxDecoration(
                                color: CupertinoColors.activeBlue,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: CupertinoColors.white,
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                CupertinoIcons.checkmark,
                                color: CupertinoColors.white,
                                size: 12,
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                );
              }
              // 非选择模式下使用 GridView.builder
              else {
                return GridView.builder(
                  padding: EdgeInsets.only(top: 8, bottom: bottomPadding),
                  itemCount: controller.images.length,
                  gridDelegate: gridDelegate,
                  itemBuilder: (context, index) {
                    final imageInfo = controller.images[index];
                    // 正常模式下的图片项
                    return CupertinoContextMenu(
                      enableHapticFeedback: true,
                      actions: [
                        CupertinoContextMenuAction(
                          onPressed: () async {
                            // 弹出
                            Navigator.of(context).pop();
                            // 获取当前图片的ID
                            final imageId = imageInfo.id;
                            // 弹出选择相簿的 Sheet
                            int? groupId = await showSelectImageGroupSheet(
                              context,
                              title: "添加到相簿",
                            );
                            // 如果没有选择相簿，直接返回
                            if (groupId == null) {
                              return;
                            }
                            // 将图片添加到相簿
                            await ImageGroupService.addImageToGroup(
                              groupId,
                              imageId,
                            );
                          },
                          trailingIcon:
                              CupertinoIcons.rectangle_stack_badge_plus,
                          child: const Text('添加到相簿'),
                        ),
                        // 删除图片
                        CupertinoContextMenuAction(
                          onPressed: () {
                            // 先关闭上下文菜单
                            Navigator.of(context).pop();
                            // 获取图片对象
                            var imageInfo = controller.images[index];
                            // 弹出提示
                            showCupertinoDialog(
                              context: context,
                              builder:
                                  (context) => CupertinoAlertDialog(
                                    title: const Text('要删除图片吗？'),
                                    content: Text(
                                      '将会在 “七海” 中删除 “${imageInfo.name}” 。',
                                    ),
                                    actions: [
                                      CupertinoDialogAction(
                                        child: const Text('取消'),
                                        onPressed: () => Navigator.pop(context),
                                      ),
                                      CupertinoDialogAction(
                                        isDestructiveAction: true,
                                        child: const Text('删除'),
                                        onPressed: () async {
                                          // 关闭对话框
                                          Navigator.pop(context);
                                          // 调用控制器的删除方法
                                          final result = await controller
                                              .deleteImage(imageInfo.id);
                                          if (!result) {
                                            // 删除失败，可以在这里添加错误提示
                                            controller.logger.e(
                                              "删除图片失败: ${imageInfo.id}",
                                            );
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                            );
                          },
                          isDestructiveAction: true,
                          trailingIcon: CupertinoIcons.delete,
                          child: const Text('删除'),
                        ),
                      ],
                      child: ExtendedImage.file(
                        File(imageInfo.thumbnailPath),
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                );
              }
            }),
          );
        }),
      ),
    );
  }
}
