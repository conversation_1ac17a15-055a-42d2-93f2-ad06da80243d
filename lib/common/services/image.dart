// 图片服务
import 'dart:io';
import 'package:drift/drift.dart' as drift;
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/add_image_controller.dart';
import 'package:nanami_flutter/common/database/database.dart';
import 'package:nanami_flutter/common/models/image.dart';
import 'package:nanami_flutter/common/services/database_service.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/common/utils/path.dart';
import 'package:path/path.dart' as path;
import 'package:ulid/ulid.dart';

abstract final class ImageService {
  // 获取数据库实例
  static Database _getDatabase() {
    return Get.find<DatabaseService>().database;
  }

  // 添加图片，返回主键 ID
  static Future<int> add(PendingImage image) async {
    try {
      // 获取图片的宽高
      final int width = image.assetEntity.width;
      final int height = image.assetEntity.height;

      // 获取后缀
      String imgExtension = path.extension(image.fileName);

      // 图片保存文件名
      String imgSaveName = "${Ulid().toString()}$imgExtension";

      // 拼接图片保存路径
      String imgSavePath = path.join(PathUtils.imageDir, imgSaveName);

      // 蒙版保存文件名
      String? maskSaveName;

      // 蒙版是否存在
      bool hasMask = image.maskFilePath != null;

      // 如果蒙版存在
      if (hasMask) {
        // 给蒙版文件名赋值
        maskSaveName = "${Ulid().toString()}.jpg";

        // 拼接蒙版保存路径
        String maskSavePath = path.join(
          PathUtils.persistentMaskDir,
          maskSaveName,
        );

        // 复制蒙版文件到持久化目录
        await File(image.maskFilePath!).copy(maskSavePath);
      }

      // 复制图片文件到图片目录
      await File(image.filePath).copy(imgSavePath);

      // 获取图片文件大小（字节）
      final int fileSize = await File(image.filePath).length();
      LoggerUtil.logger.d("图片文件大小: $fileSize 字节");

      // 压缩图片生成缩略图
      // 缩略图文件名
      String thumbnailSaveName = "${Ulid().toString()}.webp";
      // 缩略图保存路径
      String thumbnailSavePath = path.join(
        PathUtils.thumbnailDir,
        thumbnailSaveName,
      );

      // 使用 flutter_image_compress 生成缩略图
      LoggerUtil.logger.d("开始生成缩略图: $thumbnailSavePath");
      try {
        // 计算缩放比例，确保最大尺寸为 500*500
        final int maxSize = 500;
        final double scaleWidth = width / maxSize;
        final double scaleHeight = height / maxSize;
        final double scale =
            scaleWidth > scaleHeight ? scaleWidth : scaleHeight;

        // 计算目标宽高
        final int targetWidth = (width / scale).round();
        final int targetHeight = (height / scale).round();

        LoggerUtil.logger.d(
          "原始尺寸: ${width}x$height, 目标尺寸: $targetWidth x $targetHeight",
        );

        // 压缩并保存图片
        final result = await FlutterImageCompress.compressAndGetFile(
          image.filePath,
          thumbnailSavePath,
          minWidth: targetWidth,
          minHeight: targetHeight,
          quality: 85,
          format: CompressFormat.webp,
        );

        if (result == null) {
          throw Exception("缩略图生成失败");
        }
      } catch (e) {
        LoggerUtil.logger.e("生成缩略图失败: $e");
        // 如果生成缩略图失败，使用原图作为缩略图
        await File(image.filePath).copy(thumbnailSavePath);
        LoggerUtil.logger.d("使用原图作为缩略图");
      }

      // 获取数据库实例
      final db = _getDatabase();

      // 创建图片记录
      final imageCompanion = ImagesCompanion.insert(
        name: image.name,
        fileName: imgSaveName,
        width: width,
        height: height,
        size: fileSize,
        hasMask: hasMask,
        maskFileName: drift.Value(maskSaveName),
        thumbnailFileName: thumbnailSaveName,
      );

      // 插入数据库并获取ID
      final imageId = await db.into(db.images).insert(imageCompanion);

      LoggerUtil.logger.d("添加图片成功，ID: $imageId");

      // 返回图片ID
      return imageId;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("添加图片失败: $e");
      rethrow;
    }
  }

  // 条件查询
  // imageName: 图片名称，可为空，如果不为空则进行模糊查询
  // isDesc: 排序方式，true为降序（新到旧），false为升序（旧到新），默认为true
  static Future<List<ImageInfo>> query({
    String? imageName,
    bool isDesc = true,
  }) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 构建查询
      final query = db.select(db.images);

      // 如果提供了图片名称，添加模糊查询条件
      if (imageName != null && imageName.isNotEmpty) {
        query.where((tbl) => tbl.name.like('%$imageName%'));
      }

      // 添加排序条件
      if (isDesc) {
        // 降序排列（新到旧）
        query.orderBy([(t) => drift.OrderingTerm.desc(t.createdAt)]);
      } else {
        // 升序排列（旧到新）
        query.orderBy([(t) => drift.OrderingTerm.asc(t.createdAt)]);
      }

      // 执行查询
      final results = await query.get();

      // 将数据库结果转换为 ImageInfo 对象列表
      return results.map((image) {
        // 构建图片路径
        final String imagePath = path.join(PathUtils.imageDir, image.fileName);

        // 构建蒙版路径（如果存在）
        String? maskPath;
        if (image.hasMask && image.maskFileName != null) {
          maskPath = path.join(PathUtils.persistentMaskDir, image.maskFileName);
        }

        // 构建缩略图路径
        final String thumbnailPath = path.join(
          PathUtils.thumbnailDir,
          image.thumbnailFileName,
        );

        // 返回 ImageInfo 对象
        return ImageInfo(
          id: image.id,
          name: image.name.obs,
          path: imagePath,
          width: image.width,
          height: image.height,
          size: image.size,
          maskPath: maskPath,
          thumbnailPath: thumbnailPath,
          createdAt: image.createdAt,
        );
      }).toList();
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("查询图片失败: $e");
      rethrow;
    }
  }

  // 通过 ID 获取图片
  static Future<ImageInfo?> getById(int id) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 查询图片
      final image =
          await (db.select(db.images)
            ..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

      // 如果图片不存在
      if (image == null) {
        return null;
      }

      // 构建图片路径
      final String imagePath = path.join(PathUtils.imageDir, image.fileName);

      // 构建蒙版路径（如果存在）
      String? maskPath;
      if (image.hasMask && image.maskFileName != null) {
        maskPath = path.join(PathUtils.persistentMaskDir, image.maskFileName);
      }

      // 构建缩略图路径
      final String thumbnailPath = path.join(
        PathUtils.thumbnailDir,
        image.thumbnailFileName,
      );

      // 返回 ImageInfo 对象
      return ImageInfo(
        id: image.id,
        name: image.name.obs,
        path: imagePath,
        width: image.width,
        height: image.height,
        size: image.size,
        maskPath: maskPath,
        thumbnailPath: thumbnailPath,
        createdAt: image.createdAt,
      );
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("获取图片详情失败: $e");
      rethrow;
    }
  }

  // 删除图片
  static Future<bool> delete(int id) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 先获取图片信息，以便后续删除文件
      final image =
          await (db.select(db.images)
            ..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

      // 如果图片不存在
      if (image == null) {
        LoggerUtil.logger.w("要删除的图片不存在，ID: $id");
        return false;
      }

      // 开启事务
      return await db.transaction(() async {
        // 1. 删除图片与相簿的绑定记录
        await (db.delete(db.imageGroupRelation)
          ..where((tbl) => tbl.imageId.equals(id))).go();
        LoggerUtil.logger.d("删除图片与相簿的绑定记录成功，图片ID: $id");

        // 2. 删除图片记录
        final count =
            await (db.delete(db.images)
              ..where((tbl) => tbl.id.equals(id))).go();
        LoggerUtil.logger.d("删除图片记录成功，图片ID: $id, 影响行数: $count");

        // 3. 删除本地文件
        try {
          // 删除原图
          final String imagePath = path.join(
            PathUtils.imageDir,
            image.fileName,
          );
          final File imageFile = File(imagePath);
          if (await imageFile.exists()) {
            await imageFile.delete();
            LoggerUtil.logger.d("删除原图文件成功: $imagePath");
          }

          // 删除缩略图
          final String thumbnailPath = path.join(
            PathUtils.thumbnailDir,
            image.thumbnailFileName,
          );
          final File thumbnailFile = File(thumbnailPath);
          if (await thumbnailFile.exists()) {
            await thumbnailFile.delete();
            LoggerUtil.logger.d("删除缩略图文件成功: $thumbnailPath");
          }

          // 删除蒙版图片（如果存在）
          if (image.hasMask && image.maskFileName != null) {
            final String maskPath = path.join(
              PathUtils.persistentMaskDir,
              image.maskFileName,
            );
            final File maskFile = File(maskPath);
            if (await maskFile.exists()) {
              await maskFile.delete();
              LoggerUtil.logger.d("删除蒙版文件成功: $maskPath");
            }
          }
        } catch (e) {
          // 记录文件删除错误，但不影响事务
          LoggerUtil.logger.e("删除图片文件失败: $e");
        }

        return count > 0;
      });
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("删除图片失败: $e");
      rethrow;
    }
  }
}
