// 相簿服务
import 'package:drift/drift.dart' as drift;
import 'package:get/get.dart';
import 'package:nanami_flutter/common/database/database.dart';
import 'package:nanami_flutter/common/models/image.dart';
import 'package:nanami_flutter/common/models/image_group.dart';
import 'package:nanami_flutter/common/services/database_service.dart';
import 'package:nanami_flutter/common/services/image.dart';
import 'package:nanami_flutter/common/utils/logger.dart';

abstract final class ImageGroupService {
  // 获取数据库实例
  static Database _getDatabase() {
    return Get.find<DatabaseService>().database;
  }

  // 添加相簿，返回主键 ID
  static Future<int> add(String name) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 创建相簿记录
      final imageGroupCompanion = ImageGroupsCompanion.insert(name: name);

      // 插入数据库并获取ID
      final imageGroupId = await db
          .into(db.imageGroups)
          .insert(imageGroupCompanion);

      LoggerUtil.logger.d("添加相簿成功，ID: $imageGroupId");

      // 返回相簿ID
      return imageGroupId;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("添加相簿失败: $e");
      rethrow;
    }
  }

  // 更新相簿
  static Future<bool> update(int id, String name) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 更新相簿记录
      final count = await (db.update(db.imageGroups)..where(
        (tbl) => tbl.id.equals(id),
      )).write(ImageGroupsCompanion(name: drift.Value(name)));

      LoggerUtil.logger.d("更新相簿成功，ID: $id, 影响行数: $count");

      // 返回是否成功
      return count > 0;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("更新相簿失败: $e");
      rethrow;
    }
  }

  // 删除相簿
  static Future<bool> delete(int id) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 开启事务
      return await db.transaction(() async {
        // 先删除关联表中的记录
        await (db.delete(db.imageGroupRelation)
          ..where((tbl) => tbl.imageGroupId.equals(id))).go();

        // 再删除相簿记录
        final count =
            await (db.delete(db.imageGroups)
              ..where((tbl) => tbl.id.equals(id))).go();

        LoggerUtil.logger.d("删除相簿成功，ID: $id, 影响行数: $count");

        // 返回是否成功
        return count > 0;
      });
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("删除相簿失败: $e");
      rethrow;
    }
  }

  // 条件查询
  // groupName: 相簿名称，可为空，如果不为空则进行模糊查询
  // isDesc: 排序方式，true为降序（新到旧），false为升序（旧到新），默认为true
  static Future<List<ImageGroupInfo>> query({
    String? groupName,
    bool isDesc = true,
  }) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 构建查询
      final query = db.select(db.imageGroups);

      // 如果提供了相簿名称，添加模糊查询条件
      if (groupName != null && groupName.isNotEmpty) {
        query.where((tbl) => tbl.name.like('%$groupName%'));
      }

      // 添加排序条件
      if (isDesc) {
        // 降序排列（新到旧）
        query.orderBy([(t) => drift.OrderingTerm.desc(t.createdAt)]);
      } else {
        // 升序排列（旧到新）
        query.orderBy([(t) => drift.OrderingTerm.asc(t.createdAt)]);
      }

      // 执行查询
      final results = await query.get();

      // 将数据库结果转换为 ImageGroupInfo 对象列表
      final List<ImageGroupInfo> imageGroups = [];

      for (final group in results) {
        // 查询该相簿下的所有图片
        final images = await getImagesInGroup(group.id);

        // 创建 ImageGroupInfo 对象
        final imageGroupInfo = ImageGroupInfo(
          id: group.id,
          name: group.name.obs,
          images: images.obs,
          createdAt: group.createdAt,
        );

        imageGroups.add(imageGroupInfo);
      }

      return imageGroups;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("查询相簿失败: $e");
      rethrow;
    }
  }

  // 获取指定相簿的详细信息
  static Future<ImageGroupInfo?> getById(int id) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 查询相簿
      final group =
          await (db.select(db.imageGroups)
            ..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

      // 如果相簿不存在
      if (group == null) {
        return null;
      }

      // 查询该相簿下的所有图片
      final images = await getImagesInGroup(id);

      // 创建 ImageGroupInfo 对象
      return ImageGroupInfo(
        id: group.id,
        name: group.name.obs,
        images: images.obs,
        createdAt: group.createdAt,
      );
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("获取相簿详情失败: $e");
      rethrow;
    }
  }

  // 获取相簿中的所有图片
  static Future<List<ImageInfo>> getImagesInGroup(int groupId) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 查询相簿关联表，获取所有图片ID
      final query =
          db.select(db.imageGroupRelation)
            ..where((tbl) => tbl.imageGroupId.equals(groupId))
            ..orderBy([(t) => drift.OrderingTerm.desc(t.createdAt)]);

      // 执行查询
      final relations = await query.get();

      // 获取所有图片信息
      final List<ImageInfo> images = [];
      for (final relation in relations) {
        final imageInfo = await ImageService.getById(relation.imageId);
        if (imageInfo != null) {
          images.add(imageInfo);
        }
      }

      return images;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("获取相簿中的图片失败: $e");
      rethrow;
    }
  }

  // 添加图片到相簿
  static Future<bool> addImageToGroup(int groupId, int imageId) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 先检查记录是否已存在
      final existingRecord =
          await (db.select(db.imageGroupRelation)..where(
            (tbl) =>
                tbl.imageGroupId.equals(groupId) & tbl.imageId.equals(imageId),
          )).getSingleOrNull();

      // 如果记录已存在，直接返回 false
      if (existingRecord != null) {
        LoggerUtil.logger.d("图片已存在于相簿中，相簿ID: $groupId, 图片ID: $imageId");
        return false;
      }

      // 创建关联记录
      final relation = ImageGroupRelationCompanion.insert(
        imageGroupId: groupId,
        imageId: imageId,
      );

      // 插入数据库
      await db.into(db.imageGroupRelation).insert(relation);

      LoggerUtil.logger.d("添加图片到相簿成功，相簿ID: $groupId, 图片ID: $imageId");

      return true;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("添加图片到相簿失败: $e");
      // 如果是唯一约束错误，返回 false 而不是抛出异常
      if (e.toString().contains("UNIQUE constraint failed")) {
        return false;
      }
      rethrow;
    }
  }

  // 从相簿中删除图片
  static Future<bool> removeImageFromGroup(int groupId, int imageId) async {
    try {
      // 获取数据库实例
      final db = _getDatabase();

      // 删除关联记录
      final count =
          await (db.delete(db.imageGroupRelation)..where(
            (tbl) =>
                tbl.imageGroupId.equals(groupId) & tbl.imageId.equals(imageId),
          )).go();

      LoggerUtil.logger.d(
        "从相簿中删除图片成功，相簿ID: $groupId, 图片ID: $imageId, 影响行数: $count",
      );

      return count > 0;
    } catch (e) {
      // 记录错误
      LoggerUtil.logger.e("从相簿中删除图片失败: $e");
      rethrow;
    }
  }
}
