import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

class NotificationService extends GetxService {
  static NotificationService get to => Get.find<NotificationService>();

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // 通知点击响应
  final BehaviorSubject<NotificationResponse> onNotificationClick =
      BehaviorSubject<NotificationResponse>();

  // 是否初始化完成
  final RxBool _isInitialized = false.obs;
  bool get isInitialized => _isInitialized.value;

  @override
  void onInit() {
    super.onInit();
    _initializeNotifications();
  }

  // 初始化通知
  Future<void> _initializeNotifications() async {
    try {
      LoggerUtil.logger.d('开始初始化通知服务...');

      // 初始化时区数据
      tz_data.initializeTimeZones();

      // Android初始化设置
      const AndroidInitializationSettings androidInitializationSettings =
          AndroidInitializationSettings('@mipmap/launcher_icon');

      // iOS初始化设置 - 先不请求权限，后面手动请求
      final DarwinInitializationSettings darwinInitializationSettings =
          DarwinInitializationSettings(
            // 初始化时不自动请求权限，改为手动请求
            requestAlertPermission: false,
            requestBadgePermission: false,
            requestSoundPermission: false,
          );

      // 整合平台设置
      final InitializationSettings initializationSettings =
          InitializationSettings(
            android: androidInitializationSettings,
            iOS: darwinInitializationSettings,
            macOS: darwinInitializationSettings,
          );

      // 初始化插件
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      // 创建默认通知渠道
      await _createDefaultNotificationChannels();

      // 获取通知启动详情
      final NotificationAppLaunchDetails? notificationAppLaunchDetails =
          await flutterLocalNotificationsPlugin
              .getNotificationAppLaunchDetails();

      // 处理通过通知启动的情况
      if (notificationAppLaunchDetails?.didNotificationLaunchApp ?? false) {
        LoggerUtil.logger.d('应用是通过通知启动的');
        if (notificationAppLaunchDetails?.notificationResponse != null) {
          _onDidReceiveNotificationResponse(
            notificationAppLaunchDetails!.notificationResponse!,
          );
        }
      }

      // 如果是iOS平台，请求权限
      if (Platform.isIOS) {
        await _requestIOSPermissions();
      }

      _isInitialized.value = true;
      LoggerUtil.logger.d('通知服务初始化成功');
    } catch (e, s) {
      LoggerUtil.logger.e('通知服务初始化失败', error: e, stackTrace: s);
    }
  }

  // 专门为iOS请求通知权限
  Future<void> _requestIOSPermissions() async {
    LoggerUtil.logger.d('请求iOS通知权限...');
    final bool? result = await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin
        >()
        ?.requestPermissions(alert: true, badge: true, sound: true);

    LoggerUtil.logger.d('iOS通知权限请求结果: ${result ?? false}');
  }

  // 检查iOS通知权限状态
  Future<bool> checkIOSNotificationPermissionStatus() async {
    if (!Platform.isIOS) {
      return true; // 非iOS平台默认返回true
    }

    try {
      final IOSFlutterLocalNotificationsPlugin? iosPlugin =
          flutterLocalNotificationsPlugin
              .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin
              >();

      if (iosPlugin == null) {
        LoggerUtil.logger.e('无法获取iOS通知插件实例');
        return false;
      }

      // 检查权限 - 在新版本中返回的是一个布尔值
      final permissionStatus = await iosPlugin.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
      LoggerUtil.logger.d('iOS通知权限状态: $permissionStatus');
      return permissionStatus ?? false;
    } catch (e) {
      LoggerUtil.logger.e('检查iOS通知权限时出错', error: e);
      return false;
    }
  }

  // 接收通知响应
  void _onDidReceiveNotificationResponse(NotificationResponse response) {
    LoggerUtil.logger.d(
      '接收到通知响应: ${response.id}, payload: ${response.payload}',
    );
    onNotificationClick.add(response);
  }

  // 显示简单通知
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? sound,
    String? payload,
  }) async {
    if (!isInitialized) {
      LoggerUtil.logger.w('通知服务未初始化，无法显示通知');
      return;
    }

    final AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
          'nanami_high_priority',
          '高优先级通知',
          channelDescription: '用于显示重要的通知',
          importance: Importance.max,
          priority: Priority.max,
          // 以下是确保通知以悬浮形式显示的关键设置
          fullScreenIntent: true,
          category: AndroidNotificationCategory.call, // 使用call类别增加优先级
          visibility: NotificationVisibility.public,
          ongoing: false,
          autoCancel: true,
          // 声音设置 - 使用系统默认声音
          playSound: true,
          // 如果提供了自定义声音名称则使用，否则使用系统默认声音
          sound:
              sound != null ? RawResourceAndroidNotificationSound(sound) : null,
          enableVibration: true,
          channelShowBadge: true,
          showWhen: true,
        );

    final DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          // iOS 和 macOS 的声音设置 - 使用系统默认声音
          sound: sound,
        );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: darwinNotificationDetails,
      macOS: darwinNotificationDetails,
    );

    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // 显示带图片的通知 (Android专用)
  Future<void> showBigPictureNotification({
    required int id,
    required String title,
    required String body,
    required String imagePath,
    String? sound,
    String? payload,
  }) async {
    if (!isInitialized || !Platform.isAndroid) {
      LoggerUtil.logger.w('不支持的平台或通知服务未初始化');
      return;
    }

    final AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
          'nanami_image_channel',
          '图片通知频道',
          channelDescription: '用于显示带图片的通知',
          importance: Importance.max,
          priority: Priority.max,
          largeIcon: FilePathAndroidBitmap(imagePath),
          styleInformation: BigPictureStyleInformation(
            FilePathAndroidBitmap(imagePath),
            hideExpandedLargeIcon: true,
          ),
          // 以下是确保通知以悬浮形式显示的关键设置
          fullScreenIntent: true,
          category: AndroidNotificationCategory.call,
          visibility: NotificationVisibility.public,
          ongoing: false,
          autoCancel: true,
          // 声音设置
          playSound: true,
          sound:
              sound != null ? RawResourceAndroidNotificationSound(sound) : null,
          enableVibration: true,
          channelShowBadge: true,
          showWhen: true,
        );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
    );

    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // 安排定时通知
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    if (!isInitialized) {
      LoggerUtil.logger.w('通知服务未初始化，无法安排通知');
      return;
    }

    final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(
      scheduledTime,
      tz.local,
    );

    const AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
          'nanami_scheduled_channel',
          '定时通知频道',
          channelDescription: '用于显示定时通知',
          importance: Importance.max,
          priority: Priority.high,
          // 确保显示为悬浮通知
          fullScreenIntent: true,
          playSound: true,
          enableVibration: true,
          // 自动取消
          autoCancel: true,
        );

    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails();

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: darwinNotificationDetails,
      macOS: darwinNotificationDetails,
    );

    await flutterLocalNotificationsPlugin.zonedSchedule(
      id,
      title,
      body,
      tzScheduledTime,
      notificationDetails,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      payload: payload,
    );
  }

  // 取消指定通知
  Future<void> cancelNotification(int id) async {
    if (!isInitialized) return;
    await flutterLocalNotificationsPlugin.cancel(id);
  }

  // 取消所有通知
  Future<void> cancelAllNotifications() async {
    if (!isInitialized) return;
    await flutterLocalNotificationsPlugin.cancelAll();
  }

  // 创建通知频道 (仅Android)
  Future<void> createNotificationChannel({
    required String id,
    required String name,
    required String description,
    Importance importance = Importance.high,
    bool enableVibration = true,
    bool enableLights = true,
    String? soundSource,
  }) async {
    if (!isInitialized || !Platform.isAndroid) return;

    final AndroidNotificationChannel channel = AndroidNotificationChannel(
      id,
      name,
      description: description,
      importance: importance,
      enableVibration: enableVibration,
      enableLights: enableLights,
      playSound: true,
      // 如果提供了自定义声音名称则使用，否则使用系统默认声音
      sound:
          soundSource != null
              ? RawResourceAndroidNotificationSound(soundSource)
              : null,
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);
  }

  // 在初始化时预创建所有通知渠道
  Future<void> _createDefaultNotificationChannels() async {
    if (!Platform.isAndroid) return;

    // 创建默认通知渠道
    await createNotificationChannel(
      id: 'nanami_default_channel',
      name: '默认通知频道',
      description: '应用的默认通知频道',
      importance: Importance.high,
      // 使用系统默认声音，不指定soundSource
    );

    // 创建高优先级通知渠道
    await createNotificationChannel(
      id: 'nanami_high_priority',
      name: '高优先级通知',
      description: '用于显示重要的通知',
      importance: Importance.max,
      // 使用系统默认声音，不指定soundSource
    );

    // 创建图片通知渠道
    await createNotificationChannel(
      id: 'nanami_image_channel',
      name: '图片通知频道',
      description: '用于显示带图片的通知',
      importance: Importance.max,
      // 使用系统默认声音，不指定soundSource
    );

    // 创建定时通知渠道
    await createNotificationChannel(
      id: 'nanami_scheduled_channel',
      name: '定时通知频道',
      description: '用于显示定时通知',
      importance: Importance.max,
      // 使用系统默认声音，不指定soundSource
    );
  }

  @override
  void onClose() {
    onNotificationClick.close();
    super.onClose();
  }

  // 测试通知功能 - 增强版
  Future<void> testNotification() async {
    // 检查初始化状态
    if (!isInitialized) {
      LoggerUtil.logger.e('通知服务未初始化，无法发送测试通知');
      return;
    }

    // 如果是iOS平台，确保已请求权限并检查权限状态
    if (Platform.isIOS) {
      await _requestIOSPermissions();

      final bool hasPermission = await checkIOSNotificationPermissionStatus();
      if (!hasPermission) {
        LoggerUtil.logger.e('iOS通知权限未获取，无法发送通知');
        return;
      }
    }

    // 打印通知服务状态
    LoggerUtil.logger.d('发送通知之前的状态检查:');
    LoggerUtil.logger.d('通知服务初始化状态: ${_isInitialized.value}');
    LoggerUtil.logger.d('当前平台: ${Platform.operatingSystem}');

    // 简单通知
    try {
      await showNotification(
        id: 1,
        title: '测试通知',
        body: '这是一条测试通知，应以悬浮形式显示并带有声音',
        // 使用系统默认声音，不指定sound参数
      );

      LoggerUtil.logger.d('测试通知已发送成功');
    } catch (e) {
      LoggerUtil.logger.e('发送测试通知失败', error: e);
    }
  }
}
