// 数据库服务
import 'package:get/get.dart';
import 'package:nanami_flutter/common/database/database.dart';

/// 数据库服务，用于管理数据库实例
class DatabaseService extends GetxService {
  // 数据库实例
  late final Database _database;

  // 获取数据库实例
  Database get database => _database;

  // 初始化数据库
  Future<DatabaseService> init() async {
    // 创建数据库实例
    _database = Database();
    return this;
  }

  // 关闭数据库
  Future<void> close() async {
    await _database.close();
  }
}
