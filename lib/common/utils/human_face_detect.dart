import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image/image.dart' as img;
import 'package:image_size_getter/image_size_getter.dart';
import 'package:image_size_getter/file_input.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/common/utils/path.dart';
import 'package:ulid/ulid.dart';

class HumanFaceDetectUtils {
  // 人脸轮廓中头部特征点索引
  static final List<int> _headLandmarkIndices = [
    // 左边头部(从左额到中间)
    31,
    32,
    33,
    34,
    35,
    // 右边头部(从中间到右额)
    0,
    1,
    2,
    3,
    4,
    5,
  ];

  // 单例的 FaceDetector 实例
  static FaceDetector? _faceDetector;

  // 获取 FaceDetector 实例
  static FaceDetector get faceDetector {
    if (_faceDetector == null) {
      final options = FaceDetectorOptions(
        enableContours: true, // 启用轮廓检测
        performanceMode: FaceDetectorMode.fast, // 使用精确模式
        // enableLandmarks: true, // 启用关键点检测
      );
      _faceDetector = FaceDetector(options: options);
    }
    return _faceDetector!;
  }

  // 释放 FaceDetector 资源
  static Future<void> disposeFaceDetector() async {
    if (_faceDetector != null) {
      await _faceDetector!.close();
      _faceDetector = null;
    }
  }

  // 获取脸部轮廓遮罩蒙版，返回蒙版路径
  static Future<String> getFaceContoursMask(String imagePath) async {
    try {
      // 生成蒙版图片的保存路径
      String maskPath = '${PathUtils.tempMaskDir}/${Ulid().toString()}.jpg';

      // 1. 加载原始图片
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw Exception('图片文件不存在: $imagePath');
      }

      // 2. 使用 ML Kit 进行人脸检测
      final InputImage inputImage = InputImage.fromFilePath(imagePath);
      final List<Face> faces = await faceDetector.processImage(inputImage);

      // 3. 获取图片尺寸（只读取元数据，不解析整个图片）
      final sizeResult = ImageSizeGetter.getSizeResult(FileInput(imageFile));
      final imageSize = sizeResult.size;

      // 确定图片的实际宽高（考虑旋转）
      final int imageWidth =
          imageSize.needRotate ? imageSize.height : imageSize.width;
      final int imageHeight =
          imageSize.needRotate ? imageSize.width : imageSize.height;

      // 注意：在此方法中我们不需要读取图片字节数据，因为我们只需要尺寸信息

      // 4. 创建一个白色背景的图片，尺寸与原图相同
      final img.Image maskImage = img.Image(
        width: imageWidth,
        height: imageHeight,
        format: img.Format.uint8,
      );

      // 填充白色背景
      img.fill(maskImage, color: img.ColorRgb8(255, 255, 255));

      // 5. 处理每个检测到的人脸
      for (final Face face in faces) {
        // 获取人脸轮廓点
        final contour = face.contours[FaceContourType.face];
        if (contour != null && contour.points.isNotEmpty) {
          // 将轮廓点转换为图像坐标系中的点
          final List<img.Point> points = [];

          for (final point in contour.points) {
            points.add(img.Point(point.x.toInt(), point.y.toInt()));
          }

          // 使用黑色填充人脸轮廓内的区域
          if (points.length > 2) {
            img.fillPolygon(
              maskImage,
              vertices: points,
              color: img.ColorRgb8(0, 0, 0),
            );
          }
        }
      }

      // 6. 保存蒙版图片
      final File maskFile = File(maskPath);
      await maskFile.writeAsBytes(img.encodeJpg(maskImage));

      return maskPath;
    } catch (e) {
      debugPrint('创建人脸蒙版失败: $e');
      rethrow;
    }
  }

  /// 获取自动扩展的人脸轮廓蒙版，返回蒙版路径
  /// 自动扩展上部区域的轮廓点，使其覆盖到额头
  /// [expansionRate] 扩展倍率，默认为0。正值表示扩大轮廓，负值表示缩小轮廓。
  /// 例如：5表示扩大5%，-5表示缩小5%
  /// 如果没有识别到人脸，返回null
  static Future<String?> getAutoFaceContoursMask(
    String imagePath, {
    int expansionRate = 0,
  }) async {
    try {
      // 生成蒙版图片的保存路径
      String maskPath = '${PathUtils.tempMaskDir}/${Ulid().toString()}.jpg';

      // 1. 加载原始图片
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw Exception('图片文件不存在: $imagePath');
      }

      // 2. 使用 ML Kit 进行人脸检测
      final InputImage inputImage = InputImage.fromFilePath(imagePath);
      final List<Face> faces = await faceDetector.processImage(inputImage);

      LoggerUtil.logger.d('识别到人脸: ${faces.length}');

      // 如果没有识别到人脸，返回null
      if (faces.length < 1) {
        LoggerUtil.logger.d('没有识别到人脸');
        return null;
      }

      // 遍历每个人脸并打印详细信息
      for (int i = 0; i < faces.length; i++) {
        final Face face = faces[i];
        LoggerUtil.logger.d('人脸 $i 详细信息:');
        LoggerUtil.logger.d('  边界框: ${face.boundingBox}');
        LoggerUtil.logger.d('  头部欧拉角Y: ${face.headEulerAngleY}');
        LoggerUtil.logger.d('  头部欧拉角Z: ${face.headEulerAngleZ}');
        LoggerUtil.logger.d('  左眼睁开概率: ${face.leftEyeOpenProbability}');
        LoggerUtil.logger.d('  右眼睁开概率: ${face.rightEyeOpenProbability}');
        LoggerUtil.logger.d('  微笑概率: ${face.smilingProbability}');
        LoggerUtil.logger.d('  跟踪ID: ${face.trackingId}');

        // 打印轮廓信息
        if (face.contours.isNotEmpty) {
          LoggerUtil.logger.d('  轮廓数量: ${face.contours.length}');
          face.contours.forEach((type, contour) {
            LoggerUtil.logger.d(
              '    轮廓类型: $type, 点数: ${contour?.points.map((e) => e.x)}',
            );
          });
        } else {
          LoggerUtil.logger.d('  没有轮廓');
        }

        // // 打印关键点信息
        // if (face.landmarks.isNotEmpty) {
        //   LoggerUtil.logger.d('  关键点数量: ${face.landmarks.length}');
        //   face.landmarks.forEach((type, landmark) {
        //     LoggerUtil.logger.d('    关键点类型: $type, 位置: ${landmark?.position}');
        //   });
        // } else {
        //   LoggerUtil.logger.d('  没有关键点');
        // }
      }

      // 3. 获取图片尺寸（只读取元数据，不解析整个图片）
      final sizeResult = ImageSizeGetter.getSizeResult(FileInput(imageFile));
      final imageSize = sizeResult.size;

      // 确定图片的实际宽高（考虑旋转）
      final int imageWidth =
          imageSize.needRotate ? imageSize.height : imageSize.width;
      final int imageHeight =
          imageSize.needRotate ? imageSize.width : imageSize.height;

      // 4. 将图像处理部分放在compute中执行（在Isolate中）
      // 注意：不需要传递原图片数据，只需要尺寸和人脸信息
      final result = await compute(_processImageInIsolate, {
        'faces': _serializeFaces(faces),
        'expansionRate': expansionRate,
        'maskPath': maskPath,
        'headLandmarkIndices': _headLandmarkIndices,
        'imageWidth': imageWidth,
        'imageHeight': imageHeight,
      });

      LoggerUtil.logger.d('创建自动扩展人脸轮廓蒙版结果: $result');

      return result ? maskPath : null;
    } catch (e) {
      LoggerUtil.logger.e('创建自动扩展人脸轮廓蒙版失败: $e');
      return null; // 返回null而不是抛出异常，避免UI崩溃
    }
  }

  // 将Face对象序列化为可以在Isolate间传递的Map
  static List<Map<String, dynamic>> _serializeFaces(List<Face> faces) {
    return faces.map((face) {
      // 提取轮廓点
      final contourPoints =
          face.contours[FaceContourType.face]?.points
              .map((point) => {'x': point.x, 'y': point.y})
              .toList() ??
          [];

      // 提取边界框
      final boundingBox = {
        'left': face.boundingBox.left,
        'top': face.boundingBox.top,
        'right': face.boundingBox.right,
        'bottom': face.boundingBox.bottom,
      };

      return {'contourPoints': contourPoints, 'boundingBox': boundingBox};
    }).toList();
  }

  /// 在独立Isolate中处理图像
  static Future<bool> _processImageInIsolate(
    Map<String, dynamic> params,
  ) async {
    try {
      final List<dynamic> serializedFaces = params['faces'];
      final int expansionRate = params['expansionRate'];
      final String maskPath = params['maskPath'];
      final List<int> headLandmarkIndices = params['headLandmarkIndices'];

      // 使用传入的图片尺寸
      final int imageWidth = params['imageWidth'];
      final int imageHeight = params['imageHeight'];

      // 创建一个白色背景的图片，尺寸与原图相同
      final img.Image maskImage = img.Image(
        width: imageWidth,
        height: imageHeight,
        format: img.Format.uint8,
      );

      // 填充白色背景
      img.fill(maskImage, color: img.ColorRgb8(255, 255, 255));

      // 处理每个人脸
      for (final serializedFace in serializedFaces) {
        // 反序列化轮廓点
        final List<dynamic> contourPointsData = serializedFace['contourPoints'];
        final List<img.Point> points =
            contourPointsData
                .map((p) => img.Point(p['x'].toInt(), p['y'].toInt()))
                .toList();

        // 反序列化边界框
        final Map<String, dynamic> boundingBoxData =
            serializedFace['boundingBox'];
        final double top = boundingBoxData['top'];
        final double bottom = boundingBoxData['bottom'];

        if (points.isNotEmpty) {
          // 确保边界框在图像范围内
          final int boxTop = top.toInt().clamp(0, imageHeight - 1);

          // 计算参考点（可以是鼻子或脸部中心）
          // 这里简单使用边界框的垂直中心
          final int referenceY = ((top + bottom) / 2).toInt();

          // 计算扩展倍率
          // 如果最上方的点已经高于边界框的上边界，则不需要扩展
          final img.Point topPoint = points[0];
          if (topPoint.y > boxTop) {
            // 计算需要的扩展倍率
            final double scaleFactor =
                (referenceY - boxTop) / (referenceY - topPoint.y);

            // 对headLandmarkIndices中定义的所有上部区域点应用相同的扩展倍率
            for (int index in headLandmarkIndices) {
              if (index < points.length) {
                final img.Point originalPoint = points[index];
                // 只调整 y 坐标，保持 x 坐标不变
                final int newY =
                    (referenceY - (referenceY - originalPoint.y) * scaleFactor)
                        .toInt();
                // 确保新的 y 坐标在图像范围内
                final int clampedY = newY.clamp(0, imageHeight - 1);
                points[index] = img.Point(originalPoint.x, clampedY);
              }
            }
          }

          // 根据扩展倍率调整轮廓点
          if (expansionRate != 0 && points.length > 2) {
            // 计算轮廓的中心点
            double centerX = 0;
            double centerY = 0;
            for (final point in points) {
              centerX += point.x;
              centerY += point.y;
            }
            centerX /= points.length;
            centerY /= points.length;

            // 根据扩展倍率调整每个点
            final double scaleFactor = 1 + (expansionRate / 100); // 转换为比例因子

            for (int i = 0; i < points.length; i++) {
              final img.Point point = points[i];
              // 计算点到中心的向量
              final double dx = point.x - centerX;
              final double dy = point.y - centerY;

              // 应用缩放
              final int newX = (centerX + dx * scaleFactor).toInt();
              final int newY = (centerY + dy * scaleFactor).toInt();

              // 确保新坐标在图像范围内
              final int clampedX = newX.clamp(0, imageWidth - 1);
              final int clampedY = newY.clamp(0, imageHeight - 1);

              // 更新点
              points[i] = img.Point(clampedX, clampedY);
            }
          }

          // 使用黑色填充人脸轮廓内的区域
          if (points.length > 2) {
            img.fillPolygon(
              maskImage,
              vertices: points,
              color: img.ColorRgb8(0, 0, 0),
            );
          }
        }
      }

      // 保存蒙版图片
      final File maskFile = File(maskPath);
      await maskFile.writeAsBytes(img.encodeJpg(maskImage));

      return true; // 处理成功
    } catch (e) {
      LoggerUtil.logger.e('在Isolate中处理图像失败: $e');
      return false; // 处理失败
    }
  }

  /// 创建空白蒙版
  /// 返回蒙版文件路径
  static Future<String> createEmptyMask(String imagePath) async {
    try {
      // 生成蒙版图片的保存路径
      String maskPath = '${PathUtils.tempMaskDir}/${Ulid().toString()}.jpg';

      // 1. 加载原始图片
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw Exception('图片文件不存在: $imagePath');
      }

      // 2. 获取图片尺寸（只读取元数据，不解析整个图片）
      final sizeResult = ImageSizeGetter.getSizeResult(FileInput(imageFile));
      final imageSize = sizeResult.size;

      // 确定图片的实际宽高（考虑旋转）
      final int imageWidth =
          imageSize.needRotate ? imageSize.height : imageSize.width;
      final int imageHeight =
          imageSize.needRotate ? imageSize.width : imageSize.height;

      // 3. 创建一个白色背景的图片，尺寸与原图相同
      final img.Image maskImage = img.Image(
        width: imageWidth,
        height: imageHeight,
        format: img.Format.uint8,
      );

      // 填充白色背景
      img.fill(maskImage, color: img.ColorRgb8(255, 255, 255));

      // 4. 保存蒙版图片
      final File maskFile = File(maskPath);
      await maskFile.writeAsBytes(img.encodeJpg(maskImage));

      return maskPath;
    } catch (e) {
      debugPrint('创建空白蒙版失败: $e');
      rethrow;
    }
  }
}
