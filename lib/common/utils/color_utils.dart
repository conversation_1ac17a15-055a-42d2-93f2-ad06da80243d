/// 颜色工具类
abstract final class ColorUtil {
  /// 将十六进制颜色字符串转换为 Color 值
  ///
  /// 支持以下格式:
  /// - #RRGGBB
  /// - RRGGBB
  /// - #RGB
  /// - RGB
  ///
  /// 示例:
  /// ```dart
  /// final color1 = ColorUtil.fromHex('#ff0000');  // 红色
  /// final color2 = ColorUtil.fromHex('00ff00');   // 绿色
  /// final color3 = ColorUtil.fromHex('#f00');     // 简写红色
  /// final color4 = ColorUtil.fromHex('0f0');      // 简写绿色
  /// ```
  static int fromHex(String hexString) {
    // 移除可能存在的 # 前缀
    final hex = hexString.replaceAll('#', '');

    // 处理简写形式 (例如 #fff 转换为 #ffffff)
    final expandedHex =
        hex.length == 3 ? hex.split('').map((e) => '$e$e').join() : hex;

    // 确保是6位十六进制
    if (expandedHex.length != 6) {
      throw FormatException('Invalid hex color: $hexString');
    }

    // 添加 0xff 前缀并解析为整数
    try {
      return int.parse('0xff$expandedHex');
    } catch (e) {
      throw FormatException('Invalid hex color: $hexString');
    }
  }

  /// 将十六进制颜色字符串转换为十六进制表示的字符串（带0xff前缀）
  ///
  /// 示例:
  /// ```dart
  /// final hex = ColorUtil.toHexString('#ff0000');  // 返回 '0xffff0000'
  /// ```
  static String toHexString(String hexString) {
    return '0x${fromHex(hexString).toRadixString(16)}';
  }
}
