import 'package:logger/logger.dart';

/// 全局日志工具类
class LoggerUtil {
  // 私有构造函数，防止实例化
  LoggerUtil._();

  // 静态 Logger 实例
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      lineLength: 120, // 每行长度
      colors: true, // 彩色日志输出
      printEmojis: true, // 打印表情符号
    ),
  );

  /// 获取 Logger 实例
  static Logger get logger => _logger;

  /// 获取 Logger 实例
  static Logger get getLogger => _logger;
}

// 示例用法
void main(List<String> args) {
  LoggerUtil.logger.d('Hello, World!');
}
