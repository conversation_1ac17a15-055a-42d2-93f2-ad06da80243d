import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:toastification/toastification.dart';
import 'package:nanami_flutter/common/services/notification_service.dart';

class PermissionUtil {
  // 请求存储权限
  static Future<bool> requestStoragePermission() async {
    if (!Platform.isAndroid) {
      // 非Android平台不需要请求
      return true;
    }

    // 检查当前权限状态
    var status = await Permission.storage.status;
    LoggerUtil.logger.d('存储权限状态: $status');

    if (status.isDenied) {
      // 请求权限
      status = await Permission.storage.request();
    }

    // 对于已拒绝的权限，提示用户
    if (status.isPermanentlyDenied) {
      // 用户永久拒绝，需要前往设置页面手动授权
      _showPermissionDeniedToast('存储权限被拒绝，请前往设置页面手动授权');
      return false;
    }

    return status.isGranted;
  }

  // 请求通知权限
  static Future<bool> requestNotificationPermission() async {
    // 检查当前权限状态
    var status = await Permission.notification.status;
    LoggerUtil.logger.d('通知权限状态: $status');

    if (status.isDenied) {
      // 请求权限
      status = await Permission.notification.request();
    }

    // 对于已拒绝的权限，提示用户
    if (status.isPermanentlyDenied) {
      // 用户永久拒绝，需要前往设置页面手动授权
      _showPermissionDeniedToast('通知权限被拒绝，请前往设置页面手动授权');
      return false;
    }

    // iOS平台需要额外通过插件请求权限
    if (Platform.isIOS && status.isGranted) {
      try {
        // 通过NotificationService请求iOS通知权限
        LoggerUtil.logger.d('通过NotificationService请求iOS通知权限');
        await NotificationService.to.checkIOSNotificationPermissionStatus();
      } catch (e) {
        LoggerUtil.logger.e('请求iOS通知权限时出错', error: e);
      }
    }

    return status.isGranted;
  }

  // 显示权限被拒绝的提示
  static void _showPermissionDeniedToast(String message) {
    // toastification.show(
    //   type: ToastificationType.error,
    //   style: ToastificationStyle.fillColored,
    //   title: const Text('权限请求'),
    //   description: Text(message),
    //   autoCloseDuration: const Duration(seconds: 5),
    // );
    LoggerUtil.logger.e(message);
  }

  // 请求所有需要的权限
  static Future<void> requestAllPermissions() async {
    try {
      // 存储权限
      if (Platform.isAndroid) {
        final storageGranted = await requestStoragePermission();

        if (storageGranted) {
          LoggerUtil.logger.d('存储权限获取成功');
        } else {
          LoggerUtil.logger.d('存储权限获取失败');
        }

        // 通知权限
        final notificationGranted = await requestNotificationPermission();

        if (notificationGranted) {
          LoggerUtil.logger.d('通知权限获取成功');
        } else {
          LoggerUtil.logger.d('通知权限获取失败');
        }
      } else if (Platform.isIOS) {
        // iOS平台专门处理通知权限
        final notificationGranted = await requestNotificationPermission();

        if (notificationGranted) {
          LoggerUtil.logger.d('iOS通知权限获取成功');
        } else {
          LoggerUtil.logger.d('iOS通知权限获取失败');
        }
      }
    } catch (e) {
      LoggerUtil.logger.e('请求权限时发生错误', error: e);
    }
  }

  // 延迟请求权限（在应用启动后调用）
  static Future<void> requestAllPermissionsDelayed() async {
    // 延迟一秒后请求权限，确保应用完全启动
    await Future.delayed(const Duration(seconds: 1));
    await requestAllPermissions();
  }
}
