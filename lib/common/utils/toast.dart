import 'package:flutter/cupertino.dart';
import 'package:toastification/toastification.dart';

abstract final class ToastUtil {
  // 成功
  static ToastificationItem success({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return toastification.show(
      context: context,
      type: ToastificationType.success,
      style: ToastificationStyle.flat,
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
      description: Text(
        description,
        style: TextStyle(
          fontWeight: FontWeight.normal,
          color: CupertinoColors.secondaryLabel.resolveFrom(context),
        ),
      ),
      alignment: Alignment.topCenter,
      autoCloseDuration: const Duration(seconds: 4),
      primaryColor: CupertinoColors.activeGreen.resolveFrom(context),
      backgroundColor: CupertinoColors.systemBackground.resolveFrom(context),
      foregroundColor: CupertinoColors.label.resolveFrom(context),
      borderSide: BorderSide(
        color: CupertinoColors.systemGrey6.resolveFrom(context),
        // .withOpacity(0.5),
        // width: 0,
      ),
      borderRadius: BorderRadius.circular(12.0),
      dragToClose: true,
      applyBlurEffect: true,
      closeButton: ToastCloseButton(showType: CloseButtonShowType.onHover),
      boxShadow: lowModeShadow,
    );
  }
}
