import 'dart:io';

import 'package:downloadsfolder/downloadsfolder.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

// 临时目录
abstract final class PathUtils {
  // 临时目录
  static late String _tempDir;
  // 持久目录
  static late String _persistentDir;
  // 输出目录
  static late String _outputDir;

  // 初始化目录
  static Future<void> init() async {
    // 获取所有目录
    await _getAllDir();
    // 创建所有必须目录
    await _createAllDirs();
  }

  // 获取所有目录
  static Future<void> _getAllDir() async {
    // 给内部变量赋值
    _tempDir = (await getTemporaryDirectory()).path;
    _persistentDir = (await getApplicationSupportDirectory()).path;

    // 获取输出目录
    if (Platform.isIOS) {
      // iOS 使用 Document 目录
      final directory = await getApplicationDocumentsDirectory();
      _outputDir = directory.path;
    } else if (Platform.isAndroid) {
      // Android 使用下载目录/七海贴图
      final directory = await getDownloadDirectory();
      _outputDir = path.join(directory.path, '七海贴图');
    }
  }

  // 创建所有目录(如果不存在)ß
  static Future<void> _createAllDirs() async {
    // 创建临时蒙版目录
    await Directory(tempMaskDir).create(recursive: true);
    // 创建持久化蒙版目录
    await Directory(persistentMaskDir).create(recursive: true);
    // 创建图片目录
    await Directory(imageDir).create(recursive: true);
    // 创建缩略图目录
    await Directory(thumbnailDir).create(recursive: true);
    // 创建贴膜输出目录
    await Directory(overlayOutputDir).create(recursive: true);
  }

  // 存放临时蒙版的目录
  static get tempMaskDir => path.join(_tempDir, 'masks');

  // 存放持久化蒙版的目录
  static get persistentMaskDir => path.join(_persistentDir, 'masks');

  // 存放图片的目录
  static get imageDir => path.join(_persistentDir, 'images');

  // 存放缩略图的目录
  static get thumbnailDir => path.join(_persistentDir, 'thumbnails');

  // 存放贴膜输出的目录
  static get overlayOutputDir => path.join(_outputDir, '贴膜输出');

  // 存放 hive 的目录
  static get hiveDir => path.join(_persistentDir, 'hive');
}
