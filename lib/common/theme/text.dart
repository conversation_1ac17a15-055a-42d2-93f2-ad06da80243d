import 'package:flutter/cupertino.dart';

class MyTextStyle {
  // 私有构造函数，防止类被实例化
  MyTextStyle._();

  // 详情字体颜色
  static const CupertinoDynamicColor descriptionTextColor =
      CupertinoColors.secondaryLabel;

  // 详情字体样式
  static TextStyle descriptionTextStyle(BuildContext context) {
    return TextStyle(
      fontSize: 15,
      height: 1.3,
      letterSpacing: -0.1,
      color: descriptionTextColor.resolveFrom(context),
    );
  }
}
