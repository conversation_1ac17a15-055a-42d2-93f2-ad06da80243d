import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// 自定义主色
abstract final class MyPrimaryColor {
  // 粉色
  static const Color pink = Color(0xfffe799f);
  // 蓝色
  static const Color blue = CupertinoColors.systemBlue;
}

// 自定义常用色
abstract final class MyCommonColor {
  // 导航栏亮色
  static const Color tabBarBackgroundColorLight = Color(0xffF9F9F9);
  // 导航栏暗色
  static const Color tabBarBackgroundColorDark = Color(0xff1D1D1D);
}

// 背景颜色
abstract final class MyBgColor {
  // 导航栏颜色
  static CupertinoDynamicColor appBarBgColor(BuildContext context) {
    // 创建一个只有常规和暗色模式的动态颜色
    return CupertinoDynamicColor.withBrightness(
      color: CupertinoColors.systemBackground
          .resolveFrom(context)
          .withOpacity(0.85), // 亮色模式下的颜色
      darkColor: CupertinoColors.systemBackground
          .resolveFrom(context)
          .withOpacity(0.85), // 亮色模式下的颜色,
    );
  }

  // TabBar 标签栏颜色
  static CupertinoDynamicColor tabBarBgColor(BuildContext context) {
    // 创建一个只有常规和暗色模式的动态颜色
    return CupertinoDynamicColor.withBrightness(
      color: CupertinoColors.systemBackground
          .resolveFrom(context)
          .withOpacity(0.85), // 亮色模式下的颜色
      darkColor: CupertinoColors.systemBackground
          .resolveFrom(context)
          .withOpacity(0.85), // 亮色模式下的颜色,
    );
  }

  // 设置页导航栏颜色
  static CupertinoDynamicColor settingAppBarBgColor(BuildContext context) {
    return CupertinoDynamicColor.withBrightness(
      color: CupertinoColors.systemGroupedBackground
          .resolveFrom(context)
          .withOpacity(0.85),
      darkColor: CupertinoColors.systemGroupedBackground
          .resolveFrom(context)
          .withOpacity(0.85),
    );
  }

  // 相簿背景颜色
  static CupertinoDynamicColor imageGroupBgColor() {
    return CupertinoDynamicColor.withBrightness(
      color: Color(0xffF1F1F5),
      darkColor: Color(0xff494950),
    );
  }

  // 添加图片占位框背景颜色
  static CupertinoDynamicColor addImagePlaceholderBgColor() {
    return CupertinoDynamicColor.withBrightness(
      color: Color(0xFFF2F2F7), // 浅色模式: #F2F2F7
      darkColor: Color(0xFF1C1C1E), // 深色模式: #1C1C1E
    );
  }

  // Sheet 中文本域的背景颜色
  static CupertinoDynamicColor sheetTextFieldBgColor() {
    return CupertinoDynamicColor.withBrightness(
      color: CupertinoColors.white, // 浅色模式: #FFFFFF
      darkColor: Color(0xFF2C2C30), // 深色模式:
    );
  }

  // 空白页面占位符图标颜色
  static CupertinoDynamicColor emptyPageIconColor(BuildContext context) {
    return CupertinoDynamicColor.withBrightness(
      color: Color(0xFF8A8A8E), // 浅色模式: #8A8A8E
      darkColor: Color(0xFF8D8D94), // 深色模式: #8D8D94
    );
  }

  // Sheet 中的 FormRow 背景颜色
  static CupertinoDynamicColor sheetFormRowBgColor() {
    return CupertinoDynamicColor.withBrightness(
      color: Color(0xFFFFFFFF), // 亮色：#ffffff
      darkColor: Color(0xFF2C2C2E), // 暗色：#2c2c2e
    );
  }
}
