// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $ImagesTable extends Images with TableInfo<$ImagesTable, Image> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ImagesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    defaultValue: currentDateAndTime,
  );
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
    'name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _fileNameMeta = const VerificationMeta(
    'fileName',
  );
  @override
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
    'file_name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _widthMeta = const VerificationMeta('width');
  @override
  late final GeneratedColumn<int> width = GeneratedColumn<int>(
    'width',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _heightMeta = const VerificationMeta('height');
  @override
  late final GeneratedColumn<int> height = GeneratedColumn<int>(
    'height',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _sizeMeta = const VerificationMeta('size');
  @override
  late final GeneratedColumn<int> size = GeneratedColumn<int>(
    'size',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _hasMaskMeta = const VerificationMeta(
    'hasMask',
  );
  @override
  late final GeneratedColumn<bool> hasMask = GeneratedColumn<bool>(
    'has_mask',
    aliasedName,
    false,
    type: DriftSqlType.bool,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'CHECK ("has_mask" IN (0, 1))',
    ),
  );
  static const VerificationMeta _maskFileNameMeta = const VerificationMeta(
    'maskFileName',
  );
  @override
  late final GeneratedColumn<String> maskFileName = GeneratedColumn<String>(
    'mask_file_name',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _thumbnailFileNameMeta = const VerificationMeta(
    'thumbnailFileName',
  );
  @override
  late final GeneratedColumn<String> thumbnailFileName =
      GeneratedColumn<String>(
        'thumbnail_file_name',
        aliasedName,
        false,
        type: DriftSqlType.string,
        requiredDuringInsert: true,
      );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    createdAt,
    name,
    fileName,
    width,
    height,
    size,
    hasMask,
    maskFileName,
    thumbnailFileName,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'images';
  @override
  VerificationContext validateIntegrity(
    Insertable<Image> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('name')) {
      context.handle(
        _nameMeta,
        name.isAcceptableOrUnknown(data['name']!, _nameMeta),
      );
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('file_name')) {
      context.handle(
        _fileNameMeta,
        fileName.isAcceptableOrUnknown(data['file_name']!, _fileNameMeta),
      );
    } else if (isInserting) {
      context.missing(_fileNameMeta);
    }
    if (data.containsKey('width')) {
      context.handle(
        _widthMeta,
        width.isAcceptableOrUnknown(data['width']!, _widthMeta),
      );
    } else if (isInserting) {
      context.missing(_widthMeta);
    }
    if (data.containsKey('height')) {
      context.handle(
        _heightMeta,
        height.isAcceptableOrUnknown(data['height']!, _heightMeta),
      );
    } else if (isInserting) {
      context.missing(_heightMeta);
    }
    if (data.containsKey('size')) {
      context.handle(
        _sizeMeta,
        size.isAcceptableOrUnknown(data['size']!, _sizeMeta),
      );
    } else if (isInserting) {
      context.missing(_sizeMeta);
    }
    if (data.containsKey('has_mask')) {
      context.handle(
        _hasMaskMeta,
        hasMask.isAcceptableOrUnknown(data['has_mask']!, _hasMaskMeta),
      );
    } else if (isInserting) {
      context.missing(_hasMaskMeta);
    }
    if (data.containsKey('mask_file_name')) {
      context.handle(
        _maskFileNameMeta,
        maskFileName.isAcceptableOrUnknown(
          data['mask_file_name']!,
          _maskFileNameMeta,
        ),
      );
    }
    if (data.containsKey('thumbnail_file_name')) {
      context.handle(
        _thumbnailFileNameMeta,
        thumbnailFileName.isAcceptableOrUnknown(
          data['thumbnail_file_name']!,
          _thumbnailFileNameMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_thumbnailFileNameMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  Image map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Image(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      createdAt:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}created_at'],
          )!,
      name:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}name'],
          )!,
      fileName:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}file_name'],
          )!,
      width:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}width'],
          )!,
      height:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}height'],
          )!,
      size:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}size'],
          )!,
      hasMask:
          attachedDatabase.typeMapping.read(
            DriftSqlType.bool,
            data['${effectivePrefix}has_mask'],
          )!,
      maskFileName: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}mask_file_name'],
      ),
      thumbnailFileName:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}thumbnail_file_name'],
          )!,
    );
  }

  @override
  $ImagesTable createAlias(String alias) {
    return $ImagesTable(attachedDatabase, alias);
  }
}

class Image extends DataClass implements Insertable<Image> {
  final int id;
  final DateTime createdAt;
  final String name;
  final String fileName;
  final int width;
  final int height;
  final int size;
  final bool hasMask;
  final String? maskFileName;
  final String thumbnailFileName;
  const Image({
    required this.id,
    required this.createdAt,
    required this.name,
    required this.fileName,
    required this.width,
    required this.height,
    required this.size,
    required this.hasMask,
    this.maskFileName,
    required this.thumbnailFileName,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['name'] = Variable<String>(name);
    map['file_name'] = Variable<String>(fileName);
    map['width'] = Variable<int>(width);
    map['height'] = Variable<int>(height);
    map['size'] = Variable<int>(size);
    map['has_mask'] = Variable<bool>(hasMask);
    if (!nullToAbsent || maskFileName != null) {
      map['mask_file_name'] = Variable<String>(maskFileName);
    }
    map['thumbnail_file_name'] = Variable<String>(thumbnailFileName);
    return map;
  }

  ImagesCompanion toCompanion(bool nullToAbsent) {
    return ImagesCompanion(
      id: Value(id),
      createdAt: Value(createdAt),
      name: Value(name),
      fileName: Value(fileName),
      width: Value(width),
      height: Value(height),
      size: Value(size),
      hasMask: Value(hasMask),
      maskFileName:
          maskFileName == null && nullToAbsent
              ? const Value.absent()
              : Value(maskFileName),
      thumbnailFileName: Value(thumbnailFileName),
    );
  }

  factory Image.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return Image(
      id: serializer.fromJson<int>(json['id']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      name: serializer.fromJson<String>(json['name']),
      fileName: serializer.fromJson<String>(json['fileName']),
      width: serializer.fromJson<int>(json['width']),
      height: serializer.fromJson<int>(json['height']),
      size: serializer.fromJson<int>(json['size']),
      hasMask: serializer.fromJson<bool>(json['hasMask']),
      maskFileName: serializer.fromJson<String?>(json['maskFileName']),
      thumbnailFileName: serializer.fromJson<String>(json['thumbnailFileName']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'name': serializer.toJson<String>(name),
      'fileName': serializer.toJson<String>(fileName),
      'width': serializer.toJson<int>(width),
      'height': serializer.toJson<int>(height),
      'size': serializer.toJson<int>(size),
      'hasMask': serializer.toJson<bool>(hasMask),
      'maskFileName': serializer.toJson<String?>(maskFileName),
      'thumbnailFileName': serializer.toJson<String>(thumbnailFileName),
    };
  }

  Image copyWith({
    int? id,
    DateTime? createdAt,
    String? name,
    String? fileName,
    int? width,
    int? height,
    int? size,
    bool? hasMask,
    Value<String?> maskFileName = const Value.absent(),
    String? thumbnailFileName,
  }) => Image(
    id: id ?? this.id,
    createdAt: createdAt ?? this.createdAt,
    name: name ?? this.name,
    fileName: fileName ?? this.fileName,
    width: width ?? this.width,
    height: height ?? this.height,
    size: size ?? this.size,
    hasMask: hasMask ?? this.hasMask,
    maskFileName: maskFileName.present ? maskFileName.value : this.maskFileName,
    thumbnailFileName: thumbnailFileName ?? this.thumbnailFileName,
  );
  Image copyWithCompanion(ImagesCompanion data) {
    return Image(
      id: data.id.present ? data.id.value : this.id,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      name: data.name.present ? data.name.value : this.name,
      fileName: data.fileName.present ? data.fileName.value : this.fileName,
      width: data.width.present ? data.width.value : this.width,
      height: data.height.present ? data.height.value : this.height,
      size: data.size.present ? data.size.value : this.size,
      hasMask: data.hasMask.present ? data.hasMask.value : this.hasMask,
      maskFileName:
          data.maskFileName.present
              ? data.maskFileName.value
              : this.maskFileName,
      thumbnailFileName:
          data.thumbnailFileName.present
              ? data.thumbnailFileName.value
              : this.thumbnailFileName,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Image(')
          ..write('id: $id, ')
          ..write('createdAt: $createdAt, ')
          ..write('name: $name, ')
          ..write('fileName: $fileName, ')
          ..write('width: $width, ')
          ..write('height: $height, ')
          ..write('size: $size, ')
          ..write('hasMask: $hasMask, ')
          ..write('maskFileName: $maskFileName, ')
          ..write('thumbnailFileName: $thumbnailFileName')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
    id,
    createdAt,
    name,
    fileName,
    width,
    height,
    size,
    hasMask,
    maskFileName,
    thumbnailFileName,
  );
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Image &&
          other.id == this.id &&
          other.createdAt == this.createdAt &&
          other.name == this.name &&
          other.fileName == this.fileName &&
          other.width == this.width &&
          other.height == this.height &&
          other.size == this.size &&
          other.hasMask == this.hasMask &&
          other.maskFileName == this.maskFileName &&
          other.thumbnailFileName == this.thumbnailFileName);
}

class ImagesCompanion extends UpdateCompanion<Image> {
  final Value<int> id;
  final Value<DateTime> createdAt;
  final Value<String> name;
  final Value<String> fileName;
  final Value<int> width;
  final Value<int> height;
  final Value<int> size;
  final Value<bool> hasMask;
  final Value<String?> maskFileName;
  final Value<String> thumbnailFileName;
  const ImagesCompanion({
    this.id = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.name = const Value.absent(),
    this.fileName = const Value.absent(),
    this.width = const Value.absent(),
    this.height = const Value.absent(),
    this.size = const Value.absent(),
    this.hasMask = const Value.absent(),
    this.maskFileName = const Value.absent(),
    this.thumbnailFileName = const Value.absent(),
  });
  ImagesCompanion.insert({
    this.id = const Value.absent(),
    this.createdAt = const Value.absent(),
    required String name,
    required String fileName,
    required int width,
    required int height,
    required int size,
    required bool hasMask,
    this.maskFileName = const Value.absent(),
    required String thumbnailFileName,
  }) : name = Value(name),
       fileName = Value(fileName),
       width = Value(width),
       height = Value(height),
       size = Value(size),
       hasMask = Value(hasMask),
       thumbnailFileName = Value(thumbnailFileName);
  static Insertable<Image> custom({
    Expression<int>? id,
    Expression<DateTime>? createdAt,
    Expression<String>? name,
    Expression<String>? fileName,
    Expression<int>? width,
    Expression<int>? height,
    Expression<int>? size,
    Expression<bool>? hasMask,
    Expression<String>? maskFileName,
    Expression<String>? thumbnailFileName,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (createdAt != null) 'created_at': createdAt,
      if (name != null) 'name': name,
      if (fileName != null) 'file_name': fileName,
      if (width != null) 'width': width,
      if (height != null) 'height': height,
      if (size != null) 'size': size,
      if (hasMask != null) 'has_mask': hasMask,
      if (maskFileName != null) 'mask_file_name': maskFileName,
      if (thumbnailFileName != null) 'thumbnail_file_name': thumbnailFileName,
    });
  }

  ImagesCompanion copyWith({
    Value<int>? id,
    Value<DateTime>? createdAt,
    Value<String>? name,
    Value<String>? fileName,
    Value<int>? width,
    Value<int>? height,
    Value<int>? size,
    Value<bool>? hasMask,
    Value<String?>? maskFileName,
    Value<String>? thumbnailFileName,
  }) {
    return ImagesCompanion(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      name: name ?? this.name,
      fileName: fileName ?? this.fileName,
      width: width ?? this.width,
      height: height ?? this.height,
      size: size ?? this.size,
      hasMask: hasMask ?? this.hasMask,
      maskFileName: maskFileName ?? this.maskFileName,
      thumbnailFileName: thumbnailFileName ?? this.thumbnailFileName,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (fileName.present) {
      map['file_name'] = Variable<String>(fileName.value);
    }
    if (width.present) {
      map['width'] = Variable<int>(width.value);
    }
    if (height.present) {
      map['height'] = Variable<int>(height.value);
    }
    if (size.present) {
      map['size'] = Variable<int>(size.value);
    }
    if (hasMask.present) {
      map['has_mask'] = Variable<bool>(hasMask.value);
    }
    if (maskFileName.present) {
      map['mask_file_name'] = Variable<String>(maskFileName.value);
    }
    if (thumbnailFileName.present) {
      map['thumbnail_file_name'] = Variable<String>(thumbnailFileName.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ImagesCompanion(')
          ..write('id: $id, ')
          ..write('createdAt: $createdAt, ')
          ..write('name: $name, ')
          ..write('fileName: $fileName, ')
          ..write('width: $width, ')
          ..write('height: $height, ')
          ..write('size: $size, ')
          ..write('hasMask: $hasMask, ')
          ..write('maskFileName: $maskFileName, ')
          ..write('thumbnailFileName: $thumbnailFileName')
          ..write(')'))
        .toString();
  }
}

class $ImageGroupsTable extends ImageGroups
    with TableInfo<$ImageGroupsTable, ImageGroup> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ImageGroupsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    defaultValue: currentDateAndTime,
  );
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
    'name',
    aliasedName,
    false,
    type: DriftSqlType.string,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [id, createdAt, name];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'image_groups';
  @override
  VerificationContext validateIntegrity(
    Insertable<ImageGroup> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    if (data.containsKey('name')) {
      context.handle(
        _nameMeta,
        name.isAcceptableOrUnknown(data['name']!, _nameMeta),
      );
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ImageGroup map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ImageGroup(
      id:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}id'],
          )!,
      createdAt:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}created_at'],
          )!,
      name:
          attachedDatabase.typeMapping.read(
            DriftSqlType.string,
            data['${effectivePrefix}name'],
          )!,
    );
  }

  @override
  $ImageGroupsTable createAlias(String alias) {
    return $ImageGroupsTable(attachedDatabase, alias);
  }
}

class ImageGroup extends DataClass implements Insertable<ImageGroup> {
  final int id;
  final DateTime createdAt;
  final String name;
  const ImageGroup({
    required this.id,
    required this.createdAt,
    required this.name,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['name'] = Variable<String>(name);
    return map;
  }

  ImageGroupsCompanion toCompanion(bool nullToAbsent) {
    return ImageGroupsCompanion(
      id: Value(id),
      createdAt: Value(createdAt),
      name: Value(name),
    );
  }

  factory ImageGroup.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ImageGroup(
      id: serializer.fromJson<int>(json['id']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      name: serializer.fromJson<String>(json['name']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'name': serializer.toJson<String>(name),
    };
  }

  ImageGroup copyWith({int? id, DateTime? createdAt, String? name}) =>
      ImageGroup(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        name: name ?? this.name,
      );
  ImageGroup copyWithCompanion(ImageGroupsCompanion data) {
    return ImageGroup(
      id: data.id.present ? data.id.value : this.id,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      name: data.name.present ? data.name.value : this.name,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ImageGroup(')
          ..write('id: $id, ')
          ..write('createdAt: $createdAt, ')
          ..write('name: $name')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, createdAt, name);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ImageGroup &&
          other.id == this.id &&
          other.createdAt == this.createdAt &&
          other.name == this.name);
}

class ImageGroupsCompanion extends UpdateCompanion<ImageGroup> {
  final Value<int> id;
  final Value<DateTime> createdAt;
  final Value<String> name;
  const ImageGroupsCompanion({
    this.id = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.name = const Value.absent(),
  });
  ImageGroupsCompanion.insert({
    this.id = const Value.absent(),
    this.createdAt = const Value.absent(),
    required String name,
  }) : name = Value(name);
  static Insertable<ImageGroup> custom({
    Expression<int>? id,
    Expression<DateTime>? createdAt,
    Expression<String>? name,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (createdAt != null) 'created_at': createdAt,
      if (name != null) 'name': name,
    });
  }

  ImageGroupsCompanion copyWith({
    Value<int>? id,
    Value<DateTime>? createdAt,
    Value<String>? name,
  }) {
    return ImageGroupsCompanion(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      name: name ?? this.name,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ImageGroupsCompanion(')
          ..write('id: $id, ')
          ..write('createdAt: $createdAt, ')
          ..write('name: $name')
          ..write(')'))
        .toString();
  }
}

class $ImageGroupRelationTable extends ImageGroupRelation
    with TableInfo<$ImageGroupRelationTable, ImageGroupRelationData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ImageGroupRelationTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _imageGroupIdMeta = const VerificationMeta(
    'imageGroupId',
  );
  @override
  late final GeneratedColumn<int> imageGroupId = GeneratedColumn<int>(
    'image_group_id',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'REFERENCES image_groups (id)',
    ),
  );
  static const VerificationMeta _imageIdMeta = const VerificationMeta(
    'imageId',
  );
  @override
  late final GeneratedColumn<int> imageId = GeneratedColumn<int>(
    'image_id',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'REFERENCES images (id)',
    ),
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: false,
    defaultValue: currentDateAndTime,
  );
  @override
  List<GeneratedColumn> get $columns => [imageGroupId, imageId, createdAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'image_group_relation';
  @override
  VerificationContext validateIntegrity(
    Insertable<ImageGroupRelationData> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('image_group_id')) {
      context.handle(
        _imageGroupIdMeta,
        imageGroupId.isAcceptableOrUnknown(
          data['image_group_id']!,
          _imageGroupIdMeta,
        ),
      );
    } else if (isInserting) {
      context.missing(_imageGroupIdMeta);
    }
    if (data.containsKey('image_id')) {
      context.handle(
        _imageIdMeta,
        imageId.isAcceptableOrUnknown(data['image_id']!, _imageIdMeta),
      );
    } else if (isInserting) {
      context.missing(_imageIdMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {imageGroupId, imageId};
  @override
  ImageGroupRelationData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ImageGroupRelationData(
      imageGroupId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}image_group_id'],
          )!,
      imageId:
          attachedDatabase.typeMapping.read(
            DriftSqlType.int,
            data['${effectivePrefix}image_id'],
          )!,
      createdAt:
          attachedDatabase.typeMapping.read(
            DriftSqlType.dateTime,
            data['${effectivePrefix}created_at'],
          )!,
    );
  }

  @override
  $ImageGroupRelationTable createAlias(String alias) {
    return $ImageGroupRelationTable(attachedDatabase, alias);
  }
}

class ImageGroupRelationData extends DataClass
    implements Insertable<ImageGroupRelationData> {
  final int imageGroupId;
  final int imageId;
  final DateTime createdAt;
  const ImageGroupRelationData({
    required this.imageGroupId,
    required this.imageId,
    required this.createdAt,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['image_group_id'] = Variable<int>(imageGroupId);
    map['image_id'] = Variable<int>(imageId);
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  ImageGroupRelationCompanion toCompanion(bool nullToAbsent) {
    return ImageGroupRelationCompanion(
      imageGroupId: Value(imageGroupId),
      imageId: Value(imageId),
      createdAt: Value(createdAt),
    );
  }

  factory ImageGroupRelationData.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ImageGroupRelationData(
      imageGroupId: serializer.fromJson<int>(json['imageGroupId']),
      imageId: serializer.fromJson<int>(json['imageId']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'imageGroupId': serializer.toJson<int>(imageGroupId),
      'imageId': serializer.toJson<int>(imageId),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  ImageGroupRelationData copyWith({
    int? imageGroupId,
    int? imageId,
    DateTime? createdAt,
  }) => ImageGroupRelationData(
    imageGroupId: imageGroupId ?? this.imageGroupId,
    imageId: imageId ?? this.imageId,
    createdAt: createdAt ?? this.createdAt,
  );
  ImageGroupRelationData copyWithCompanion(ImageGroupRelationCompanion data) {
    return ImageGroupRelationData(
      imageGroupId:
          data.imageGroupId.present
              ? data.imageGroupId.value
              : this.imageGroupId,
      imageId: data.imageId.present ? data.imageId.value : this.imageId,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ImageGroupRelationData(')
          ..write('imageGroupId: $imageGroupId, ')
          ..write('imageId: $imageId, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(imageGroupId, imageId, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ImageGroupRelationData &&
          other.imageGroupId == this.imageGroupId &&
          other.imageId == this.imageId &&
          other.createdAt == this.createdAt);
}

class ImageGroupRelationCompanion
    extends UpdateCompanion<ImageGroupRelationData> {
  final Value<int> imageGroupId;
  final Value<int> imageId;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const ImageGroupRelationCompanion({
    this.imageGroupId = const Value.absent(),
    this.imageId = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ImageGroupRelationCompanion.insert({
    required int imageGroupId,
    required int imageId,
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : imageGroupId = Value(imageGroupId),
       imageId = Value(imageId);
  static Insertable<ImageGroupRelationData> custom({
    Expression<int>? imageGroupId,
    Expression<int>? imageId,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (imageGroupId != null) 'image_group_id': imageGroupId,
      if (imageId != null) 'image_id': imageId,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ImageGroupRelationCompanion copyWith({
    Value<int>? imageGroupId,
    Value<int>? imageId,
    Value<DateTime>? createdAt,
    Value<int>? rowid,
  }) {
    return ImageGroupRelationCompanion(
      imageGroupId: imageGroupId ?? this.imageGroupId,
      imageId: imageId ?? this.imageId,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (imageGroupId.present) {
      map['image_group_id'] = Variable<int>(imageGroupId.value);
    }
    if (imageId.present) {
      map['image_id'] = Variable<int>(imageId.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ImageGroupRelationCompanion(')
          ..write('imageGroupId: $imageGroupId, ')
          ..write('imageId: $imageId, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$Database extends GeneratedDatabase {
  _$Database(QueryExecutor e) : super(e);
  $DatabaseManager get managers => $DatabaseManager(this);
  late final $ImagesTable images = $ImagesTable(this);
  late final $ImageGroupsTable imageGroups = $ImageGroupsTable(this);
  late final $ImageGroupRelationTable imageGroupRelation =
      $ImageGroupRelationTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
    images,
    imageGroups,
    imageGroupRelation,
  ];
  @override
  DriftDatabaseOptions get options =>
      const DriftDatabaseOptions(storeDateTimeAsText: true);
}

typedef $$ImagesTableCreateCompanionBuilder =
    ImagesCompanion Function({
      Value<int> id,
      Value<DateTime> createdAt,
      required String name,
      required String fileName,
      required int width,
      required int height,
      required int size,
      required bool hasMask,
      Value<String?> maskFileName,
      required String thumbnailFileName,
    });
typedef $$ImagesTableUpdateCompanionBuilder =
    ImagesCompanion Function({
      Value<int> id,
      Value<DateTime> createdAt,
      Value<String> name,
      Value<String> fileName,
      Value<int> width,
      Value<int> height,
      Value<int> size,
      Value<bool> hasMask,
      Value<String?> maskFileName,
      Value<String> thumbnailFileName,
    });

final class $$ImagesTableReferences
    extends BaseReferences<_$Database, $ImagesTable, Image> {
  $$ImagesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<
    $ImageGroupRelationTable,
    List<ImageGroupRelationData>
  >
  _imageGroupRelationRefsTable(_$Database db) => MultiTypedResultKey.fromTable(
    db.imageGroupRelation,
    aliasName: $_aliasNameGenerator(
      db.images.id,
      db.imageGroupRelation.imageId,
    ),
  );

  $$ImageGroupRelationTableProcessedTableManager get imageGroupRelationRefs {
    final manager = $$ImageGroupRelationTableTableManager(
      $_db,
      $_db.imageGroupRelation,
    ).filter((f) => f.imageId.id.sqlEquals($_itemColumn<int>('id')!));

    final cache = $_typedResult.readTableOrNull(
      _imageGroupRelationRefsTable($_db),
    );
    return ProcessedTableManager(
      manager.$state.copyWith(prefetchedData: cache),
    );
  }
}

class $$ImagesTableFilterComposer extends Composer<_$Database, $ImagesTable> {
  $$ImagesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get fileName => $composableBuilder(
    column: $table.fileName,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get width => $composableBuilder(
    column: $table.width,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get height => $composableBuilder(
    column: $table.height,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get size => $composableBuilder(
    column: $table.size,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<bool> get hasMask => $composableBuilder(
    column: $table.hasMask,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get maskFileName => $composableBuilder(
    column: $table.maskFileName,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get thumbnailFileName => $composableBuilder(
    column: $table.thumbnailFileName,
    builder: (column) => ColumnFilters(column),
  );

  Expression<bool> imageGroupRelationRefs(
    Expression<bool> Function($$ImageGroupRelationTableFilterComposer f) f,
  ) {
    final $$ImageGroupRelationTableFilterComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.id,
      referencedTable: $db.imageGroupRelation,
      getReferencedColumn: (t) => t.imageId,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImageGroupRelationTableFilterComposer(
            $db: $db,
            $table: $db.imageGroupRelation,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return f(composer);
  }
}

class $$ImagesTableOrderingComposer extends Composer<_$Database, $ImagesTable> {
  $$ImagesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get fileName => $composableBuilder(
    column: $table.fileName,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get width => $composableBuilder(
    column: $table.width,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get height => $composableBuilder(
    column: $table.height,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get size => $composableBuilder(
    column: $table.size,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<bool> get hasMask => $composableBuilder(
    column: $table.hasMask,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get maskFileName => $composableBuilder(
    column: $table.maskFileName,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get thumbnailFileName => $composableBuilder(
    column: $table.thumbnailFileName,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$ImagesTableAnnotationComposer
    extends Composer<_$Database, $ImagesTable> {
  $$ImagesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  GeneratedColumn<String> get fileName =>
      $composableBuilder(column: $table.fileName, builder: (column) => column);

  GeneratedColumn<int> get width =>
      $composableBuilder(column: $table.width, builder: (column) => column);

  GeneratedColumn<int> get height =>
      $composableBuilder(column: $table.height, builder: (column) => column);

  GeneratedColumn<int> get size =>
      $composableBuilder(column: $table.size, builder: (column) => column);

  GeneratedColumn<bool> get hasMask =>
      $composableBuilder(column: $table.hasMask, builder: (column) => column);

  GeneratedColumn<String> get maskFileName => $composableBuilder(
    column: $table.maskFileName,
    builder: (column) => column,
  );

  GeneratedColumn<String> get thumbnailFileName => $composableBuilder(
    column: $table.thumbnailFileName,
    builder: (column) => column,
  );

  Expression<T> imageGroupRelationRefs<T extends Object>(
    Expression<T> Function($$ImageGroupRelationTableAnnotationComposer a) f,
  ) {
    final $$ImageGroupRelationTableAnnotationComposer composer =
        $composerBuilder(
          composer: this,
          getCurrentColumn: (t) => t.id,
          referencedTable: $db.imageGroupRelation,
          getReferencedColumn: (t) => t.imageId,
          builder:
              (
                joinBuilder, {
                $addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer,
              }) => $$ImageGroupRelationTableAnnotationComposer(
                $db: $db,
                $table: $db.imageGroupRelation,
                $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                joinBuilder: joinBuilder,
                $removeJoinBuilderFromRootComposer:
                    $removeJoinBuilderFromRootComposer,
              ),
        );
    return f(composer);
  }
}

class $$ImagesTableTableManager
    extends
        RootTableManager<
          _$Database,
          $ImagesTable,
          Image,
          $$ImagesTableFilterComposer,
          $$ImagesTableOrderingComposer,
          $$ImagesTableAnnotationComposer,
          $$ImagesTableCreateCompanionBuilder,
          $$ImagesTableUpdateCompanionBuilder,
          (Image, $$ImagesTableReferences),
          Image,
          PrefetchHooks Function({bool imageGroupRelationRefs})
        > {
  $$ImagesTableTableManager(_$Database db, $ImagesTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$ImagesTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$ImagesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () => $$ImagesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<String> name = const Value.absent(),
                Value<String> fileName = const Value.absent(),
                Value<int> width = const Value.absent(),
                Value<int> height = const Value.absent(),
                Value<int> size = const Value.absent(),
                Value<bool> hasMask = const Value.absent(),
                Value<String?> maskFileName = const Value.absent(),
                Value<String> thumbnailFileName = const Value.absent(),
              }) => ImagesCompanion(
                id: id,
                createdAt: createdAt,
                name: name,
                fileName: fileName,
                width: width,
                height: height,
                size: size,
                hasMask: hasMask,
                maskFileName: maskFileName,
                thumbnailFileName: thumbnailFileName,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                required String name,
                required String fileName,
                required int width,
                required int height,
                required int size,
                required bool hasMask,
                Value<String?> maskFileName = const Value.absent(),
                required String thumbnailFileName,
              }) => ImagesCompanion.insert(
                id: id,
                createdAt: createdAt,
                name: name,
                fileName: fileName,
                width: width,
                height: height,
                size: size,
                hasMask: hasMask,
                maskFileName: maskFileName,
                thumbnailFileName: thumbnailFileName,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          $$ImagesTableReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: ({imageGroupRelationRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (imageGroupRelationRefs) db.imageGroupRelation,
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (imageGroupRelationRefs)
                    await $_getPrefetchedData<
                      Image,
                      $ImagesTable,
                      ImageGroupRelationData
                    >(
                      currentTable: table,
                      referencedTable: $$ImagesTableReferences
                          ._imageGroupRelationRefsTable(db),
                      managerFromTypedResult:
                          (p0) =>
                              $$ImagesTableReferences(
                                db,
                                table,
                                p0,
                              ).imageGroupRelationRefs,
                      referencedItemsForCurrentItem:
                          (item, referencedItems) => referencedItems.where(
                            (e) => e.imageId == item.id,
                          ),
                      typedResults: items,
                    ),
                ];
              },
            );
          },
        ),
      );
}

typedef $$ImagesTableProcessedTableManager =
    ProcessedTableManager<
      _$Database,
      $ImagesTable,
      Image,
      $$ImagesTableFilterComposer,
      $$ImagesTableOrderingComposer,
      $$ImagesTableAnnotationComposer,
      $$ImagesTableCreateCompanionBuilder,
      $$ImagesTableUpdateCompanionBuilder,
      (Image, $$ImagesTableReferences),
      Image,
      PrefetchHooks Function({bool imageGroupRelationRefs})
    >;
typedef $$ImageGroupsTableCreateCompanionBuilder =
    ImageGroupsCompanion Function({
      Value<int> id,
      Value<DateTime> createdAt,
      required String name,
    });
typedef $$ImageGroupsTableUpdateCompanionBuilder =
    ImageGroupsCompanion Function({
      Value<int> id,
      Value<DateTime> createdAt,
      Value<String> name,
    });

final class $$ImageGroupsTableReferences
    extends BaseReferences<_$Database, $ImageGroupsTable, ImageGroup> {
  $$ImageGroupsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<
    $ImageGroupRelationTable,
    List<ImageGroupRelationData>
  >
  _imageGroupRelationRefsTable(_$Database db) => MultiTypedResultKey.fromTable(
    db.imageGroupRelation,
    aliasName: $_aliasNameGenerator(
      db.imageGroups.id,
      db.imageGroupRelation.imageGroupId,
    ),
  );

  $$ImageGroupRelationTableProcessedTableManager get imageGroupRelationRefs {
    final manager = $$ImageGroupRelationTableTableManager(
      $_db,
      $_db.imageGroupRelation,
    ).filter((f) => f.imageGroupId.id.sqlEquals($_itemColumn<int>('id')!));

    final cache = $_typedResult.readTableOrNull(
      _imageGroupRelationRefsTable($_db),
    );
    return ProcessedTableManager(
      manager.$state.copyWith(prefetchedData: cache),
    );
  }
}

class $$ImageGroupsTableFilterComposer
    extends Composer<_$Database, $ImageGroupsTable> {
  $$ImageGroupsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnFilters(column),
  );

  Expression<bool> imageGroupRelationRefs(
    Expression<bool> Function($$ImageGroupRelationTableFilterComposer f) f,
  ) {
    final $$ImageGroupRelationTableFilterComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.id,
      referencedTable: $db.imageGroupRelation,
      getReferencedColumn: (t) => t.imageGroupId,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImageGroupRelationTableFilterComposer(
            $db: $db,
            $table: $db.imageGroupRelation,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return f(composer);
  }
}

class $$ImageGroupsTableOrderingComposer
    extends Composer<_$Database, $ImageGroupsTable> {
  $$ImageGroupsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get name => $composableBuilder(
    column: $table.name,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$ImageGroupsTableAnnotationComposer
    extends Composer<_$Database, $ImageGroupsTable> {
  $$ImageGroupsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<String> get name =>
      $composableBuilder(column: $table.name, builder: (column) => column);

  Expression<T> imageGroupRelationRefs<T extends Object>(
    Expression<T> Function($$ImageGroupRelationTableAnnotationComposer a) f,
  ) {
    final $$ImageGroupRelationTableAnnotationComposer composer =
        $composerBuilder(
          composer: this,
          getCurrentColumn: (t) => t.id,
          referencedTable: $db.imageGroupRelation,
          getReferencedColumn: (t) => t.imageGroupId,
          builder:
              (
                joinBuilder, {
                $addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer,
              }) => $$ImageGroupRelationTableAnnotationComposer(
                $db: $db,
                $table: $db.imageGroupRelation,
                $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
                joinBuilder: joinBuilder,
                $removeJoinBuilderFromRootComposer:
                    $removeJoinBuilderFromRootComposer,
              ),
        );
    return f(composer);
  }
}

class $$ImageGroupsTableTableManager
    extends
        RootTableManager<
          _$Database,
          $ImageGroupsTable,
          ImageGroup,
          $$ImageGroupsTableFilterComposer,
          $$ImageGroupsTableOrderingComposer,
          $$ImageGroupsTableAnnotationComposer,
          $$ImageGroupsTableCreateCompanionBuilder,
          $$ImageGroupsTableUpdateCompanionBuilder,
          (ImageGroup, $$ImageGroupsTableReferences),
          ImageGroup,
          PrefetchHooks Function({bool imageGroupRelationRefs})
        > {
  $$ImageGroupsTableTableManager(_$Database db, $ImageGroupsTable table)
    : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$ImageGroupsTableFilterComposer($db: db, $table: table),
          createOrderingComposer:
              () => $$ImageGroupsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer:
              () =>
                  $$ImageGroupsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<String> name = const Value.absent(),
              }) => ImageGroupsCompanion(
                id: id,
                createdAt: createdAt,
                name: name,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                required String name,
              }) => ImageGroupsCompanion.insert(
                id: id,
                createdAt: createdAt,
                name: name,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          $$ImageGroupsTableReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: ({imageGroupRelationRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (imageGroupRelationRefs) db.imageGroupRelation,
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (imageGroupRelationRefs)
                    await $_getPrefetchedData<
                      ImageGroup,
                      $ImageGroupsTable,
                      ImageGroupRelationData
                    >(
                      currentTable: table,
                      referencedTable: $$ImageGroupsTableReferences
                          ._imageGroupRelationRefsTable(db),
                      managerFromTypedResult:
                          (p0) =>
                              $$ImageGroupsTableReferences(
                                db,
                                table,
                                p0,
                              ).imageGroupRelationRefs,
                      referencedItemsForCurrentItem:
                          (item, referencedItems) => referencedItems.where(
                            (e) => e.imageGroupId == item.id,
                          ),
                      typedResults: items,
                    ),
                ];
              },
            );
          },
        ),
      );
}

typedef $$ImageGroupsTableProcessedTableManager =
    ProcessedTableManager<
      _$Database,
      $ImageGroupsTable,
      ImageGroup,
      $$ImageGroupsTableFilterComposer,
      $$ImageGroupsTableOrderingComposer,
      $$ImageGroupsTableAnnotationComposer,
      $$ImageGroupsTableCreateCompanionBuilder,
      $$ImageGroupsTableUpdateCompanionBuilder,
      (ImageGroup, $$ImageGroupsTableReferences),
      ImageGroup,
      PrefetchHooks Function({bool imageGroupRelationRefs})
    >;
typedef $$ImageGroupRelationTableCreateCompanionBuilder =
    ImageGroupRelationCompanion Function({
      required int imageGroupId,
      required int imageId,
      Value<DateTime> createdAt,
      Value<int> rowid,
    });
typedef $$ImageGroupRelationTableUpdateCompanionBuilder =
    ImageGroupRelationCompanion Function({
      Value<int> imageGroupId,
      Value<int> imageId,
      Value<DateTime> createdAt,
      Value<int> rowid,
    });

final class $$ImageGroupRelationTableReferences
    extends
        BaseReferences<
          _$Database,
          $ImageGroupRelationTable,
          ImageGroupRelationData
        > {
  $$ImageGroupRelationTableReferences(
    super.$_db,
    super.$_table,
    super.$_typedResult,
  );

  static $ImageGroupsTable _imageGroupIdTable(_$Database db) =>
      db.imageGroups.createAlias(
        $_aliasNameGenerator(
          db.imageGroupRelation.imageGroupId,
          db.imageGroups.id,
        ),
      );

  $$ImageGroupsTableProcessedTableManager get imageGroupId {
    final $_column = $_itemColumn<int>('image_group_id')!;

    final manager = $$ImageGroupsTableTableManager(
      $_db,
      $_db.imageGroups,
    ).filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_imageGroupIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
      manager.$state.copyWith(prefetchedData: [item]),
    );
  }

  static $ImagesTable _imageIdTable(_$Database db) => db.images.createAlias(
    $_aliasNameGenerator(db.imageGroupRelation.imageId, db.images.id),
  );

  $$ImagesTableProcessedTableManager get imageId {
    final $_column = $_itemColumn<int>('image_id')!;

    final manager = $$ImagesTableTableManager(
      $_db,
      $_db.images,
    ).filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_imageIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
      manager.$state.copyWith(prefetchedData: [item]),
    );
  }
}

class $$ImageGroupRelationTableFilterComposer
    extends Composer<_$Database, $ImageGroupRelationTable> {
  $$ImageGroupRelationTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );

  $$ImageGroupsTableFilterComposer get imageGroupId {
    final $$ImageGroupsTableFilterComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.imageGroupId,
      referencedTable: $db.imageGroups,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImageGroupsTableFilterComposer(
            $db: $db,
            $table: $db.imageGroups,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }

  $$ImagesTableFilterComposer get imageId {
    final $$ImagesTableFilterComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.imageId,
      referencedTable: $db.images,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImagesTableFilterComposer(
            $db: $db,
            $table: $db.images,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }
}

class $$ImageGroupRelationTableOrderingComposer
    extends Composer<_$Database, $ImageGroupRelationTable> {
  $$ImageGroupRelationTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );

  $$ImageGroupsTableOrderingComposer get imageGroupId {
    final $$ImageGroupsTableOrderingComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.imageGroupId,
      referencedTable: $db.imageGroups,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImageGroupsTableOrderingComposer(
            $db: $db,
            $table: $db.imageGroups,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }

  $$ImagesTableOrderingComposer get imageId {
    final $$ImagesTableOrderingComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.imageId,
      referencedTable: $db.images,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImagesTableOrderingComposer(
            $db: $db,
            $table: $db.images,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }
}

class $$ImageGroupRelationTableAnnotationComposer
    extends Composer<_$Database, $ImageGroupRelationTable> {
  $$ImageGroupRelationTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  $$ImageGroupsTableAnnotationComposer get imageGroupId {
    final $$ImageGroupsTableAnnotationComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.imageGroupId,
      referencedTable: $db.imageGroups,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImageGroupsTableAnnotationComposer(
            $db: $db,
            $table: $db.imageGroups,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }

  $$ImagesTableAnnotationComposer get imageId {
    final $$ImagesTableAnnotationComposer composer = $composerBuilder(
      composer: this,
      getCurrentColumn: (t) => t.imageId,
      referencedTable: $db.images,
      getReferencedColumn: (t) => t.id,
      builder:
          (
            joinBuilder, {
            $addJoinBuilderToRootComposer,
            $removeJoinBuilderFromRootComposer,
          }) => $$ImagesTableAnnotationComposer(
            $db: $db,
            $table: $db.images,
            $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
            joinBuilder: joinBuilder,
            $removeJoinBuilderFromRootComposer:
                $removeJoinBuilderFromRootComposer,
          ),
    );
    return composer;
  }
}

class $$ImageGroupRelationTableTableManager
    extends
        RootTableManager<
          _$Database,
          $ImageGroupRelationTable,
          ImageGroupRelationData,
          $$ImageGroupRelationTableFilterComposer,
          $$ImageGroupRelationTableOrderingComposer,
          $$ImageGroupRelationTableAnnotationComposer,
          $$ImageGroupRelationTableCreateCompanionBuilder,
          $$ImageGroupRelationTableUpdateCompanionBuilder,
          (ImageGroupRelationData, $$ImageGroupRelationTableReferences),
          ImageGroupRelationData,
          PrefetchHooks Function({bool imageGroupId, bool imageId})
        > {
  $$ImageGroupRelationTableTableManager(
    _$Database db,
    $ImageGroupRelationTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer:
              () => $$ImageGroupRelationTableFilterComposer(
                $db: db,
                $table: table,
              ),
          createOrderingComposer:
              () => $$ImageGroupRelationTableOrderingComposer(
                $db: db,
                $table: table,
              ),
          createComputedFieldComposer:
              () => $$ImageGroupRelationTableAnnotationComposer(
                $db: db,
                $table: table,
              ),
          updateCompanionCallback:
              ({
                Value<int> imageGroupId = const Value.absent(),
                Value<int> imageId = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => ImageGroupRelationCompanion(
                imageGroupId: imageGroupId,
                imageId: imageId,
                createdAt: createdAt,
                rowid: rowid,
              ),
          createCompanionCallback:
              ({
                required int imageGroupId,
                required int imageId,
                Value<DateTime> createdAt = const Value.absent(),
                Value<int> rowid = const Value.absent(),
              }) => ImageGroupRelationCompanion.insert(
                imageGroupId: imageGroupId,
                imageId: imageId,
                createdAt: createdAt,
                rowid: rowid,
              ),
          withReferenceMapper:
              (p0) =>
                  p0
                      .map(
                        (e) => (
                          e.readTable(table),
                          $$ImageGroupRelationTableReferences(db, table, e),
                        ),
                      )
                      .toList(),
          prefetchHooksCallback: ({imageGroupId = false, imageId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                T extends TableManagerState<
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic,
                  dynamic
                >
              >(state) {
                if (imageGroupId) {
                  state =
                      state.withJoin(
                            currentTable: table,
                            currentColumn: table.imageGroupId,
                            referencedTable: $$ImageGroupRelationTableReferences
                                ._imageGroupIdTable(db),
                            referencedColumn:
                                $$ImageGroupRelationTableReferences
                                    ._imageGroupIdTable(db)
                                    .id,
                          )
                          as T;
                }
                if (imageId) {
                  state =
                      state.withJoin(
                            currentTable: table,
                            currentColumn: table.imageId,
                            referencedTable: $$ImageGroupRelationTableReferences
                                ._imageIdTable(db),
                            referencedColumn:
                                $$ImageGroupRelationTableReferences
                                    ._imageIdTable(db)
                                    .id,
                          )
                          as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ),
      );
}

typedef $$ImageGroupRelationTableProcessedTableManager =
    ProcessedTableManager<
      _$Database,
      $ImageGroupRelationTable,
      ImageGroupRelationData,
      $$ImageGroupRelationTableFilterComposer,
      $$ImageGroupRelationTableOrderingComposer,
      $$ImageGroupRelationTableAnnotationComposer,
      $$ImageGroupRelationTableCreateCompanionBuilder,
      $$ImageGroupRelationTableUpdateCompanionBuilder,
      (ImageGroupRelationData, $$ImageGroupRelationTableReferences),
      ImageGroupRelationData,
      PrefetchHooks Function({bool imageGroupId, bool imageId})
    >;

class $DatabaseManager {
  final _$Database _db;
  $DatabaseManager(this._db);
  $$ImagesTableTableManager get images =>
      $$ImagesTableTableManager(_db, _db.images);
  $$ImageGroupsTableTableManager get imageGroups =>
      $$ImageGroupsTableTableManager(_db, _db.imageGroups);
  $$ImageGroupRelationTableTableManager get imageGroupRelation =>
      $$ImageGroupRelationTableTableManager(_db, _db.imageGroupRelation);
}
