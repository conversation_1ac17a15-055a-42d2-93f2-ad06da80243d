import 'package:drift/drift.dart';
import 'package:drift_flutter/drift_flutter.dart';
import 'package:path_provider/path_provider.dart';
part 'database.g.dart';

@DriftDatabase(tables: [Images, ImageGroups, ImageGroupRelation])
class Database extends _$Database {
  Database([QueryExecutor? executor]) : super(executor ?? _openConnection());

  @override
  int get schemaVersion => 1;

  static QueryExecutor _openConnection() {
    return driftDatabase(
      name: 'nanami',
      native: const DriftNativeOptions(
        databaseDirectory: getApplicationSupportDirectory,
      ),
    );
  }
}

// 通用表的 mixin
mixin CommonTableMixin on Table {
  // ID
  IntColumn get id => integer().autoIncrement()();
  // 创建时间
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
}

// 图片表
class Images extends Table with CommonTableMixin {
  // 图片名称
  TextColumn get name => text()();
  // 图片文件名
  TextColumn get fileName => text()();
  // 图片宽度
  IntColumn get width => integer()();
  // 图片高度
  IntColumn get height => integer()();
  // 图片大小（Byte）
  IntColumn get size => integer()();
  // 是否存在遮罩
  BoolColumn get hasMask => boolean()();
  // 遮罩文件名
  TextColumn get maskFileName => text().nullable()();
  // 缩略图文件名
  TextColumn get thumbnailFileName => text()();
}

// 相簿表
class ImageGroups extends Table with CommonTableMixin {
  // 相簿名称
  TextColumn get name => text()();
}

// 图片与分组的关联表
class ImageGroupRelation extends Table {
  // 相簿ID
  IntColumn get imageGroupId => integer().references(ImageGroups, #id)();
  // 图片ID
  IntColumn get imageId => integer().references(Images, #id)();
  // 创建时间
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  // 设置联合主键
  @override
  Set<Column> get primaryKey => {imageGroupId, imageId};
}
