import 'package:flutter/cupertino.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';

/// 亮度模式枚举
enum BrightnessMode {
  /// 自动（跟随系统）
  auto,

  /// 浅色模式
  light,

  /// 深色模式
  dark,
}

// 预览遮罩混合模式枚举
enum ShowBlendMode {
  /// 正片叠底
  multiply,

  // 滤色
  screen,
}

/// 设置盒子类，用于管理应用设置
class SettingBox {
  /// 私有构造函数，防止外部实例化
  SettingBox._();

  /// 设置盒子名称
  static const String _boxName = 'setting';

  /// 亮度模式键名
  static const String _brightnessKey = 'brightness';

  // 预览遮罩不透明度键名
  static const String _showMaskOpacityKey = 'showMaskOpacity';

  // 预览遮罩混合模式键名
  static const String _showMaskBlendModeKey = 'showMaskBlendMode';

  // 人脸检测扩展倍率键名
  static const String _faceDetectExpansionRateKey = 'faceDetectExpansionRate';

  /// 当前盒子
  static final Box _box = Hive.box(_boxName);

  // 暴露盒子
  static Box get box => _box;

  /// 获取当前亮度模式
  /// 默认为自动模式
  static BrightnessMode get brightnessMode {
    final String? value = _box.get(_brightnessKey) as String?;

    // 如果值为空，返回默认值（自动）
    if (value == null) {
      return BrightnessMode.auto;
    }

    // 根据存储的字符串值返回对应的枚举值
    return BrightnessMode.values.firstWhere(
      (mode) => mode.toString() == 'BrightnessMode.$value',
      orElse: () => BrightnessMode.auto,
    );
  }

  /// 设置亮度模式
  static Future<void> setBrightnessMode(BrightnessMode mode) async {
    // 从枚举值中提取名称部分（例如：ShowBlendMode.auto -> auto）
    final String modeName = mode.toString().split('.').last;
    await _box.put(_brightnessKey, modeName);
  }

  /// 获取亮度值
  /// 根据当前亮度模式和系统亮度返回对应的亮度值
  static Brightness getBrightness(BuildContext context) {
    switch (brightnessMode) {
      case BrightnessMode.auto:
        // 自动模式下跟随系统亮度
        return MediaQuery.of(context).platformBrightness;
      case BrightnessMode.light:
        return Brightness.light;
      case BrightnessMode.dark:
        return Brightness.dark;
    }
  }

  // 获取遮罩不透明度
  static double getMaskOpacity() {
    return _box.get(_showMaskOpacityKey, defaultValue: 0.5);
  }

  // 设置遮罩不透明度
  static Future<void> setMaskOpacity(double opacity) async {
    await _box.put(_showMaskOpacityKey, opacity);
  }

  // 设置预览遮罩混合模式
  static Future<void> setShowMaskBlendMode(ShowBlendMode mode) async {
    // 从枚举值中提取名称部分（例如：ShowBlendMode.multiply -> multiply
    final String modeName = mode.toString().split('.').last;
    await _box.put(_showMaskBlendModeKey, modeName);
  }

  // 获取预览遮罩混合模式
  static BlendMode getShowMaskBlendMode() {
    // 获取值
    final String value = _box.get(
      _showMaskBlendModeKey,
      defaultValue: 'multiply',
    );

    if (value == 'multiply') {
      return BlendMode.multiply;
    } else {
      return BlendMode.screen;
    }
  }

  // 获取人脸检测扩展倍率
  static int getFaceDetectExpansionRate() {
    return _box.get(_faceDetectExpansionRateKey, defaultValue: 0);
  }

  // 设置人脸检测扩展倍率
  static Future<void> setFaceDetectExpansionRate(int rate) async {
    await _box.put(_faceDetectExpansionRateKey, rate);
  }
}
