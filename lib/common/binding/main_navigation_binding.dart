import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/main_navigation_controller.dart';
import 'package:nanami_flutter/common/controllers/my_image_groups_controller.dart';
import 'package:nanami_flutter/common/controllers/my_images_controller.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/controllers/image_overlay_controller.dart';

class MainNavigationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<MainNavigationController>(() => MainNavigationController());
    Get.lazyPut<SettingController>(() => SettingController());
    Get.lazyPut<MyImagesController>(() => MyImagesController());
    Get.lazyPut<MyImageGroupsController>(() => MyImageGroupsController());
    Get.lazyPut<ImageOverlayController>(() => ImageOverlayController());
  }
}
