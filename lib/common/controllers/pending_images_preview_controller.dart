import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/add_image_controller.dart';

class PendingImagesPreviewController extends GetxController {
  // 当前显示的图片索引
  final RxInt currentIndex = 0.obs;
  
  // 所有图片列表
  final List<PendingImage> pendingImages;
  
  // 初始索引
  final int initialIndex;
  
  PendingImagesPreviewController({
    required this.pendingImages,
    required this.initialIndex,
  }) {
    // 设置初始索引
    currentIndex.value = initialIndex;
  }
  
  // 获取当前图片
  PendingImage get currentImage => pendingImages[currentIndex.value];
  
  // 切换到指定索引的图片
  void changePage(int index) {
    if (index >= 0 && index < pendingImages.length) {
      currentIndex.value = index;
    }
  }
}
