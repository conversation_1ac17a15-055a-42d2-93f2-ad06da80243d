import 'dart:io';
import 'package:flutter/material.dart';
import 'package:nanami_flutter/common/controllers/setting_controller.dart';
import 'package:nanami_flutter/common/utils/logger.dart';
import 'package:nanami_flutter/widgets/image_tile_item.dart';
import 'package:path/path.dart' as path;
import 'package:get/get.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class AddImageController extends GetxController {
  // 用于控制AnimatedList的全局Key
  final GlobalKey<AnimatedListState> listKey = GlobalKey<AnimatedListState>();
  // 待添加的图片列表
  final RxList<PendingImage> pendingImages = <PendingImage>[].obs;
  // 设置 Controller
  final SettingController settingController = Get.find<SettingController>();

  // 获取所有资产实体的计算属性
  List<AssetEntity> get assetEntites =>
      pendingImages.map((image) => image.assetEntity).toList();

  // 添加图片
  void addPendingImage(AssetEntity assetEntity) async {
    LoggerUtil.logger.d("触发添加方法");
    // 获取原始文件
    File? file = await assetEntity.loadFile();
    if (file == null) {
      // 处理文件加载失败的情况
      return;
    }

    // 获取文件名（带扩展名）
    String fileName = path.basename(file.path);
    // 获取文件名（不带扩展名）
    String nameWithoutExtension = path.basenameWithoutExtension(file.path);

    // 创建待添加的图片对象
    final pendingImage = PendingImage(
      assetEntity: assetEntity,
      name: nameWithoutExtension, // 使用不带扩展名的文件名作为图片名称
      fileName: fileName, // 使用完整文件名（带扩展名）
      filePath: file.path, // 使用完整文件路径
    );

    // 获取当前列表长度作为插入位置
    int insertIndex = pendingImages.length;

    // 将待添加的图片添加到列表中
    pendingImages.add(pendingImage);

    // 触发AnimatedList的插入动画
    listKey.currentState?.insertItem(insertIndex);
  }

  // 根据索引删除图片
  void removePendingImageByIndex(int index) {
    // 检查索引是否有效
    if (index < 0 || index >= pendingImages.length) {
      LoggerUtil.logger.d("删除图片失败：索引无效 $index");
      return;
    }

    LoggerUtil.logger.d("删除图片，索引: $index");

    // 保存要删除的图片对象，用于构建移除动画
    final removedItem = pendingImages[index];

    // 从列表中移除图片
    pendingImages.removeAt(index);

    // 触发AnimatedList的移除动画
    listKey.currentState?.removeItem(
      index,
      (context, animation) =>
          _buildRemovedItem(removedItem, context, animation),
    );
  }

  // 构建被移除项的动画效果
  Widget _buildRemovedItem(
    PendingImage item,
    BuildContext context,
    Animation<double> animation,
  ) {
    // 使用FadeTransition和SizeTransition组合动画效果
    return SizeTransition(
      sizeFactor: animation,
      child: FadeTransition(
        opacity: animation,
        child: Column(
          children: [
            ImageTileItem(
              title: item.name,
              maskPath: item.maskFilePath,
              imagePath: item.filePath,
              maskBlendMode: BlendMode.multiply,
              maskOpacity: 0.5,
            ),
          ],
        ),
      ),
    );
  }

  // 清空所有待添加的图片
  void clearPendingImages() {
    LoggerUtil.logger.d("清空所有图片");

    // 如果列表为空，直接返回
    if (pendingImages.isEmpty) return;

    // 逐个移除项目以触发动画
    for (int i = pendingImages.length - 1; i >= 0; i--) {
      final removedItem = pendingImages[i];
      pendingImages.removeAt(i);

      listKey.currentState?.removeItem(
        i,
        (context, animation) =>
            _buildRemovedItem(removedItem, context, animation),
      );
    }
  }
}

// 待添加的图片
class PendingImage {
  // 资产实体
  final Rx<AssetEntity> _assetEntity;
  // 图片名
  final RxString _name;
  // 文件名
  final RxString _fileName;
  // 路径
  final RxString _filePath;
  // 遮罩路径
  final Rxn<String> _maskFilePath;

  // 构造函数接收普通值并将它们转换为响应式
  PendingImage({
    required AssetEntity assetEntity,
    required String name,
    required String fileName,
    required String filePath,
    String? maskFilePath,
  }) : _assetEntity = assetEntity.obs,
       _name = name.obs,
       _fileName = fileName.obs,
       _filePath = filePath.obs,
       _maskFilePath = Rxn<String>(maskFilePath);

  // Getter 方法用于访问响应式属性的值
  AssetEntity get assetEntity => _assetEntity.value;
  String get name => _name.value;
  String get fileName => _fileName.value;
  String get filePath => _filePath.value;
  String? get maskFilePath => _maskFilePath.value;

  // Setter 方法用于更新响应式属性的值
  set assetEntity(AssetEntity value) => _assetEntity.value = value;
  set name(String value) => _name.value = value;
  set fileName(String value) => _fileName.value = value;
  set filePath(String value) => _filePath.value = value;
  set maskFilePath(String? value) => _maskFilePath.value = value;
}
