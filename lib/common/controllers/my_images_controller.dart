import 'package:drag_select_grid_view/drag_select_grid_view.dart';
import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/my_image_groups_controller.dart';
import 'package:nanami_flutter/common/models/image.dart';
import 'package:nanami_flutter/common/services/image.dart';
import 'package:nanami_flutter/common/utils/logger.dart';

class MyImagesController extends GetxController {
  // 图片信息列表
  final RxList<ImageInfo> images = <ImageInfo>[].obs;

  // 选择控制器
  final selectController = DragSelectGridViewController();

  // 搜索关键词
  final RxString searchKeyWord = ''.obs;

  // 是否正在搜索
  final RxBool isSearching = false.obs;

  // 排序方式，默认为降序（新到旧）
  final RxBool isDescending = true.obs;

  // 是否正在多选
  final RxBool isMultiSelecting = false.obs;

  // 已选中的图片ID列表
  final RxList<int> selectedImageIds = <int>[].obs;

  // 日志工具
  final logger = LoggerUtil.logger;

  // 是否正在加载
  final RxBool isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    // 使用Future.microtask延迟初始化，避免阻塞主线程
    Future.microtask(() => _initImages());
    // 添加监听
    selectController.addListener(() {
      refreshSelectedImages();
    });
  }

  @override
  void onClose() {
    super.onClose();
    // 移除监听
    selectController.removeListener(() {
      refreshSelectedImages();
    });
  }

  // 初始化图片列表
  Future<void> _initImages() async {
    try {
      isLoading.value = true;
      await refreshImages();
    } catch (e) {
      logger.e("初始化图片列表失败: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // 刷新图片列表
  Future<void> refreshImages() async {
    logger.d("刷新图片列表");
    try {
      isLoading.value = true;
      images.value = await ImageService.query(
        imageName: searchKeyWord.value.isEmpty ? null : searchKeyWord.value,
        isDesc: isDescending.value,
      );
      logger.d("图片列表刷新成功，共 ${images.length} 张图片");
    } catch (e) {
      logger.e("刷新图片列表失败: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // 搜索图片
  Future<void> searchImages(String query) async {
    searchKeyWord.value = query;
    await refreshImages();
  }

  // 清除搜索
  Future<void> clearSearch() async {
    searchKeyWord.value = '';
    isSearching.value = false;
    await refreshImages();
  }

  // 切换排序方式
  Future<void> toggleSortOrder() async {
    isDescending.value = !isDescending.value;
    await refreshImages();
  }

  // 切换多选模式
  void toggleMultiSelectMode() {
    isMultiSelecting.value = !isMultiSelecting.value;
    selectController.toggleSelectionMode();
    // 如果退出多选模式，清空已选择的图片
    if (!isMultiSelecting.value) {
      clearSelectedImages();
    }
  }

  // 退出多选模式
  void exitMultiSelectMode() {
    isMultiSelecting.value = false;
    clearSelectedImages();
    selectController.disableSelectionMode();
  }

  // 清空已选择的图片
  void clearSelectedImages() {
    selectController.clear();
  }

  // 刷新已选图片列表
  void refreshSelectedImages() {
    // 通过索引获取图片ID
    selectedImageIds.value =
        selectController.value.selectedIndexes
            .map((index) => index < images.length ? images[index].id : -1)
            .where((id) => id != -1)
            .toList();
  }

  // 删除图片
  Future<bool> deleteImage(int imageId) async {
    try {
      logger.d("开始删除图片，ID: $imageId");
      isLoading.value = true;

      // 调用 ImageService 的删除方法
      final result = await ImageService.delete(imageId);

      if (result) {
        logger.d("图片删除成功，ID: $imageId");

        // 从当前列表中移除该图片
        images.removeWhere((image) => image.id == imageId);

        // 如果在多选模式下，更新已选图片列表
        if (isMultiSelecting.value) {
          refreshSelectedImages();
        }

        // 获取相簿控制器
        final MyImageGroupsController imageGroupsController =
            Get.find<MyImageGroupsController>();

        // 刷新相簿
        imageGroupsController.refreshImageGroups();
      } else {
        logger.w("图片删除失败，ID: $imageId");
      }

      return result;
    } catch (e) {
      logger.e("删除图片出错: $e");
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}
