import 'package:get/get.dart';
import 'package:nanami_flutter/common/controllers/my_images_controller.dart';

/// 主导航控制器，用于管理底部导航栏状态
class MainNavigationController extends GetxController {
  /// 当前选中的底部导航栏索引
  final RxInt currentIndex = 0.obs;

  /// 是否显示多选模式底部操作栏
  final RxBool showMultiSelectBar = false.obs;

  /// 获取图片控制器
  MyImagesController? get myImagesController =>
      currentIndex.value == 0 ? Get.find<MyImagesController>() : null;

  @override
  void onInit() {
    super.onInit();
    // 监听图片页面的多选模式状态
    ever(Get.find<MyImagesController>().isMultiSelecting, (isMultiSelecting) {
      showMultiSelectBar.value = isMultiSelecting;
    });
  }

  /// 更新当前选中的底部导航栏索引
  void changeTab(int index) {
    // 如果当前在多选模式，且要切换到其他标签页，则先退出多选模式
    if (showMultiSelectBar.value && index != 0) {
      Get.find<MyImagesController>().exitMultiSelectMode();
    }
    currentIndex.value = index;
  }
}
