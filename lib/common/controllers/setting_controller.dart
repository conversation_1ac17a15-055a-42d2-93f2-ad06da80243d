import 'package:get/get.dart';
import 'package:hive_ce_flutter/adapters.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:nanami_flutter/common/hive/setting_box.dart';

class SettingController extends GetxController {
  // 设置 Box
  static final Box _box = SettingBox.box;

  // 获取 SettingController 实例的静态方法
  static SettingController get to => Get.find<SettingController>();

  // 版本信息可观察变量
  final RxString version = 'Unknown'.obs;
  final RxBool isLoading = true.obs;

  // 亮度模式可观察变量
  final Rx<BrightnessMode> brightnessMode = BrightnessMode.auto.obs;

  // 预览混合模式可观察变量
  late Rx<BlendMode> showMaskBlendMode;

  // 预览不透明度可观察变量
  late RxDouble showMaskOpacity;

  // 人脸检测扩展倍率可观察变量
  late RxInt faceDetectExpansionRate;

  // 贴膜自动分类
  final RxBool overlayAutoClassify = RxBool(
    _box.get('overlayAutoClassify', defaultValue: true),
  );

  // 贴膜自动分类规则(watermark, base_image)
  final RxString overlayAutoClassifyBy = RxString(
    _box.get('overlayAutoClassifyBy', defaultValue: 'watermark'),
  );

  // 贴膜自动擦脸
  final RxBool overlayAutoCleanFace = RxBool(
    _box.get('overlayAutoCleanFace', defaultValue: true),
  );

  // 贴膜混合模式(normal, overlay, soft_light, screen, multiply)
  final RxString overlayBlendMode = RxString(
    _box.get('overlayBlendMode', defaultValue: 'normal'),
  );

  // 贴膜不透明度 (0-100)
  final RxInt overlayOpacity = RxInt(
    _box.get('overlayOpacity', defaultValue: 30),
  );

  // 贴膜填充方式(stretch, tile)
  final RxString overlayFillMode = RxString(
    _box.get('overlayFillMode', defaultValue: 'stretch'),
  );

  // 贴膜平铺倍率(30-300)
  final RxInt overlayTileRate = RxInt(
    _box.get('overlayTileRate', defaultValue: 100),
  );

  // 贴膜输出格式(png, jpg)
  final RxString overlayOutputFormat = RxString(
    _box.get('overlayOutputFormat', defaultValue: 'jpg'),
  );

  // 贴膜输出保存到相册
  final RxBool overlayOutputToAlbum = RxBool(
    _box.get('overlayOutputToAlbum', defaultValue: false),
  );

  void _initListener() {
    // 监听贴膜自动分类变化并同步到 Hive
    overlayAutoClassify.listen((value) {
      _box.put('overlayAutoClassify', value);
    });

    // 监听贴膜自动分类规则变化并同步到 Hive
    overlayAutoClassifyBy.listen((value) {
      _box.put('overlayAutoClassifyBy', value);
    });

    // 监听贴膜输出保存到相册变化并同步到 Hive
    overlayOutputToAlbum.listen((value) {
      _box.put('overlayOutputToAlbum', value);
    });

    // 监听贴膜自动擦脸变化并同步到 Hive
    overlayAutoCleanFace.listen((value) {
      _box.put('overlayAutoCleanFace', value);
    });

    // 监听贴膜混合模式变化并同步到 Hive
    overlayBlendMode.listen((value) {
      _box.put('overlayBlendMode', value);
    });

    // 监听贴膜不透明度变化并同步到 Hive
    overlayOpacity.listen((value) {
      _box.put('overlayOpacity', value);
    });

    // 监听贴膜填充方式变化并同步到 Hive
    overlayFillMode.listen((value) {
      _box.put('overlayFillMode', value);
    });

    // 监听贴膜平铺倍率变化并同步到 Hive
    overlayTileRate.listen((value) {
      _box.put('overlayTileRate', value);
    });

    // 监听贴膜输出格式变化并同步到 Hive
    overlayOutputFormat.listen((value) {
      _box.put('overlayOutputFormat', value);
    });
  }

  @override
  void onInit() async {
    super.onInit();
    await initPackageInfo(); // 初始化版本信息
    loadBrightnessMode(); // 加载亮度模式设置
    initShowMaskSettings(); // 初始化预览遮罩设置
    _initListener(); // 初始化监听
  }

  // 获取应用版本信息
  Future<void> initPackageInfo() async {
    try {
      final info = await PackageInfo.fromPlatform();
      version.value = info.version;
      isLoading.value = false;
    } catch (e) {
      version.value = 'Unknown';
      isLoading.value = false;
      debugPrint('获取版本信息失败: $e');
    }
  }

  // 初始化预览遮罩设置
  void initShowMaskSettings() {
    showMaskBlendMode = SettingBox.getShowMaskBlendMode().obs;
    showMaskOpacity = SettingBox.getMaskOpacity().obs;
    faceDetectExpansionRate = SettingBox.getFaceDetectExpansionRate().obs;
  }

  /// 加载亮度模式设置
  void loadBrightnessMode() {
    brightnessMode.value = SettingBox.brightnessMode;
  }

  /// 更新亮度模式
  Future<void> updateBrightnessMode(BrightnessMode mode) async {
    await SettingBox.setBrightnessMode(mode);
    brightnessMode.value = mode;
  }

  /// 获取当前亮度值
  Brightness getCurrentBrightness(BuildContext context) {
    switch (brightnessMode.value) {
      case BrightnessMode.auto:
        // 自动模式下使用系统亮度
        // 使用 MediaQuery 获取系统亮度，可以自动响应系统亮度变化
        return MediaQuery.platformBrightnessOf(context);
      case BrightnessMode.light:
        return Brightness.light;
      case BrightnessMode.dark:
        return Brightness.dark;
    }
  }

  // 更新预览遮罩混合模式
  Future<void> updateShowMaskBlendMode(ShowBlendMode mode) async {
    // 设置 box 中的值
    await SettingBox.setShowMaskBlendMode(mode);
    // 更新响应式变量
    showMaskBlendMode.value = SettingBox.getShowMaskBlendMode();
  }

  // 更新预览遮罩不透明度
  Future<void> updateShowMaskOpacity(double opacity) async {
    // 设置 box 中的值
    await SettingBox.setMaskOpacity(opacity);
    // 更新响应式变量
    showMaskOpacity.value = SettingBox.getMaskOpacity();
  }

  // 更新人脸检测扩展倍率
  Future<void> updateFaceDetectExpansionRate(int rate) async {
    // 设置 box 中的值
    await SettingBox.setFaceDetectExpansionRate(rate);
    // 更新响应式变量
    faceDetectExpansionRate.value = SettingBox.getFaceDetectExpansionRate();
  }
}
