import 'package:get/get.dart';
import 'package:nanami_flutter/common/models/image_group.dart';
import 'package:nanami_flutter/common/services/image_group.dart';
import 'package:nanami_flutter/common/utils/logger.dart';

class MyImageGroupsController extends GetxController {
  // 相簿信息列表
  final RxList<ImageGroupInfo> imageGroups = <ImageGroupInfo>[].obs;

  // 搜索关键词
  final RxString searchKeyWord = ''.obs;

  // 是否正在搜索
  final RxBool isSearching = false.obs;

  // 是否正在加载
  final RxBool isLoading = true.obs;

  // 日志工具
  final logger = LoggerUtil.logger;

  @override
  void onInit() {
    super.onInit();
    // 使用Future.microtask延迟初始化，避免阻塞主线程
    Future.microtask(() => _initImageGroups());
  }

  // 初始化相簿列表
  Future<void> _initImageGroups() async {
    try {
      isLoading.value = true;
      await refreshImageGroups();
    } catch (e) {
      logger.e("初始化相簿列表失败: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // 刷新相簿列表
  Future<void> refreshImageGroups() async {
    logger.d("刷新相簿列表");
    try {
      isLoading.value = true;
      imageGroups.value = await ImageGroupService.query(
        groupName: searchKeyWord.value.isEmpty ? null : searchKeyWord.value,
      );
      logger.d("相簿列表刷新成功，共 ${imageGroups.length} 个相簿");
    } catch (e) {
      logger.e("刷新相簿列表失败: $e");
    } finally {
      isLoading.value = false;
    }
  }

  // 搜索相簿
  Future<void> searchImageGroups(String query) async {
    searchKeyWord.value = query;
    await refreshImageGroups();
  }

  // 清除搜索
  Future<void> clearSearch() async {
    searchKeyWord.value = '';
    isSearching.value = false;
    await refreshImageGroups();
  }
}
