import 'package:get/get.dart';

class ImageInfo {
  // 图片 ID
  final int id;
  // 图片名称
  final RxString name;
  // 图片路径
  final String path;
  // 图片宽度
  final int width;
  // 图片高度
  final int height;
  // 图片大小
  final int size;
  // 遮罩路径
  final String? maskPath;
  // 缩略图路径
  final String thumbnailPath;
  // 创建时间
  final DateTime createdAt;

  const ImageInfo({
    required this.id,
    required this.name,
    required this.path,
    required this.width,
    required this.height,
    required this.size,
    required this.maskPath,
    required this.thumbnailPath,
    required this.createdAt,
  });
}
