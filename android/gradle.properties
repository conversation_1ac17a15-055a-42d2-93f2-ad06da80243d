org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
# 设置 Kotlin 编译器的 JVM 目标版本为 11，防止插件使用不兼容的版本
kotlin.jvm.target.validation.mode=warning
kotlin.compiler.execution.strategy=in-process
kotlin.jvm.target=11
# 为所有 Kotlin 任务添加 JVM 目标版本
kotlin.setJvmTargetFromAndroidCompileOptions=false
# 兼容旧版插件
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

