# 七海贴图

基于 `Flutter3 + getx + hive + drift` 的图片管理、图片处理 APP。

UI/UX 遵循 IOS 原生的 Cupertino 风格。

## 项目结构

```
lib/                    # 主要代码目录
├── common/             # 公共代码
│   ├── api/            # API接口
│   ├── binding/        # GetX 绑定
│   ├── controllers/    # GetX 控制器
│   ├── hive/           # Hive 数据库相关
│   ├── middlewares/    # GetX 路由中间件
│   ├── models/         # 数据模型
│   ├── routes/         # GetX 路由管理
│   ├── services/       # 服务
│   └── utils/          # 工具类
├── pages/              # 页面
├── widgets/            # 自定义组件
└── main.dart           # 应用入口
```


