name: nanami_flutter
description: "七海贴图"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# 以下定义了应用程序的版本和构建号。
# 版本号是由点分隔的三个数字，如 1.2.43
# 后跟一个可选的由 + 分隔的构建号。
# 版本和构建号都可以在 flutter 构建时通过
# 分别指定 --build-name 和 --build-number 来覆盖。
# 在 Android 中，build-name 用作 versionName，而 build-number 用作 versionCode。
# 了解更多关于 Android 版本控制的信息：https://developer.android.com/studio/publish/versioning
# 在 iOS 中，build-name 用作 CFBundleShortVersionString，而 build-number 用作 CFBundleVersion。
# 了解更多关于 iOS 版本控制的信息：
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# 在 Windows 中，build-name 用作产品和文件版本的主要、次要和补丁部分，而 build-number 用作构建后缀。
version: 0.1.0

environment:
  sdk: ^3.7.2

# 依赖项指定您的软件包正常工作所需的其他软件包。
# 要自动将软件包依赖项升级到最新版本，
# 考虑运行 `flutter pub upgrade --major-versions`。或者，
# 可以通过将下面的版本号更改为 pub.dev 上可用的最新版本来手动更新依赖项。
# 要查看哪些依赖项有更新的版本可用，请运行 `flutter pub outdated`。
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  # GetX
  get:
  # dio 网络请求包
  dio:
  # IOS 风格的 Sheet
  modal_bottom_sheet:
  # 又一个 IOS 风格的 Sheet
  smooth_sheets:
  # IOS 风格的滑动条
  interactive_slider:
  # IOS 风格的欢迎页
  cupertino_onboarding:
  # 超级 cupertino 导航栏
  super_cupertino_navigation_bar:
    path: vendors/super_cupertino_navigation_bar
  # Hive
  hive_ce:
  hive_ce_flutter:
  # 压缩与解压缩
  archive:
  # toast
  toastification:
  # drift ORM
  drift:
  drift_flutter:
  # 获取包信息
  package_info_plus:
  # lottie 动画
  lottie:
  # UUID
  uuid:
  ulid:
  # IOS 风格图标
  cupertino_icons:
  # APP 图标
  flutter_launcher_icons:
  # 路径处理
  path:
  # 路径提供
  path_provider:
  # 权限处理
  permission_handler:
  # 原生图片选择器
  image_picker:
  # Json 解析
  json_serializable:
  # 日志打印
  logger:
  # 启动图
  flutter_native_splash:
  # 本地通知
  flutter_local_notifications:
  # 平滑分页器
  smooth_page_indicator:
  # 保存图片到相册
  gal:
  # 微信图片选择器
  wechat_assets_picker:
  # 文件操作
  file:
  # 增强图片插件
  extended_image:
  # 振动反馈
  haptic_feedback:
  gaimon:
  # 混合遮罩
  blendmask:
  # Cupertino 下拉按钮
  pull_down_button:
  # 将组件转换为玻璃效果
  glass:
  # 脸部识别
  google_mlkit_face_detection:
  # 图像编辑
  image:
  image_editor:
  # 图片尺寸获取
  image_size_getter:
  # 绘图板
  flutter_drawing_board:
  image_painter:
  # SF 图标
  flutter_sficon:
  # 图片压缩
  flutter_image_compress:
  # IOS 风格设置页
  ce_settings:
  # 可拖拽选择网格
  drag_select_grid_view:
    path: vendors/drag_select_grid_view
  # 可读化文件大小
  filesize:
  # 点击回弹动画
  bounce:
  # 路由过渡动画
  page_transition:
  # cupertino 风格的边栏
  cupertino_sidebar: ^1.0.1
  # 覆盖加载
  loader_overlay: ^5.0.0
  # cupertino 进度条
  cupertino_progress_bar: ^0.2.0
  # 打开文件管理器
  open_file_manager: ^2.0.1
  # 处理下载文件夹
  downloadsfolder: ^1.1.0
  # 响应式编程
  rxdart:
  # 时区处理
  timezone:
  ffi: ^2.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  hive_ce_generator:
  drift_dev:
  build_runner:

  # 下面的 "flutter_lints" 包包含一组推荐的 lint 规则，
  # 以鼓励良好的编码实践。该包提供的 lint 规则集在位于
  # 软件包根目录的 `analysis_options.yaml` 文件中激活。
  # 有关禁用特定 lint 规则和激活其他规则的信息，请参阅该文件。
  flutter_lints: ^5.0.0

# 覆盖版本
dependency_overrides:
  # extended_image: ^10.0.0

# 有关此文件中通用 Dart 部分的信息，请参阅
# 以下页面：https://dart.dev/tools/pub/pubspec

# 以下部分专用于 Flutter 软件包。
flutter:
  # 以下行确保 Material Icons 字体
  # 包含在您的应用程序中，以便您可以使用
  # material Icons 类中的图标。
  uses-material-design: true

  # 要向应用程序添加资源，请添加一个资源部分，如下所示：
  assets:
    - assets/lotties/
    - assets/images/
    - assets/splash/
    - assets/fonts/

  # 图像资源可以引用一个或多个特定分辨率的"变体"，请参阅
  # https://flutter.dev/to/resolution-aware-images

  # 有关从包依赖项添加资源的详细信息，请参阅
  # https://flutter.dev/to/asset-from-package

  # 要向应用程序添加自定义字体，请在此处添加字体部分，
  # 在这个 "flutter" 部分中。此列表中的每个条目都应该有一个
  # "family" 键，其中包含字体系列名称，以及一个 "fonts" 键，其中包含
  # 提供字体资源和其他描述符的列表。例如：
  fonts:
    - family: myIcon
      fonts:
        - asset: assets/icon_fonts/iconfont.ttf
  # - family: PingFang SC
  #   fonts:
  #     - asset: assets/fonts/PingFangSC/PingFangSC-Regular.ttf
  #       weight: 400
  #     - asset: assets/fonts/PingFangSC/PingFangSC-Light.ttf
  #       weight: 300
  #     - asset: assets/fonts/PingFangSC/PingFangSC-Medium.ttf
  #       weight: 500
  #     - asset: assets/fonts/PingFangSC/PingFangSC-Semibold.ttf
  #       weight: 600
  #     - asset: assets/fonts/PingFangSC/PingFangSC-Thin.ttf
  #       weight: 100
  #     - asset: assets/fonts/PingFangSC/PingFangSC-Ultralight.ttf
  #       weight: 200
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # 有关来自包依赖项的字体的详细信息，
  # 请参阅 https://flutter.dev/to/font-from-package
