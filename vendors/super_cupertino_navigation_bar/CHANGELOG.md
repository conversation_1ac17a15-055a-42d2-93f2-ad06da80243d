## [2.1.2] - 2024-01-17
* Dart analyze pub.dev score upgraded

## [2.1.1] - 2024-01-17
* Dart analyze pub.dev score upgraded

## [2.1.0] - 2024-01-17
* Dart analyze pub.dev score upgraded
* Deprecated methods fixed
* Material widgets removed. So package can be usable both CupertinoApp and MaterialApp
* Animation Background transition corruption fixed
* The error that occurred when using another widget other than the text widget in the middle section has been fixed.
* Aimed to increase flexibility in package usage so that Package can be usable within Scaffold or CupertinoPageScaffold
* Some corruptions on collapsed appbar fixed
* SuperScaffold body attribute can now be any widget you want. SingleChildScrollView, CustomScrollView or etc...
* Readme.md updated

## [2.0.4] - 2024-01-04
* transition collapse bug on title and largetitle

## [2.0.4] - 2024-01-03
* web demo activated

## [2.0.3] - 2024-01-02
* ReadMe.md updated

## [2.0.2] - 2024-01-02
* ReadMe.md updated

## [2.0.1] - 2024-01-02
* ReadMe.md updated

## [2.0.0] - 2024-01-01
* Breaking change was made
* slivers are not available currently
* height of any widget can be adjustable now
* bottom app bar feature added
* transition between routes functionality improved
* events improved

## [1.1.3] - 2023-12-21
* "textStyle" added to searchDecorationModel
* "placeholderTextStyle" added to searchDecorationModel
* README.md updated 

## [1.1.2] - 2023-11-5
* Some Milestones added to Readme

## [1.1.1] - 2023-11-5
* Android Platform bugs is now fixed

## [1.1.0] - 2023-10-23
* Search Field Action Buttons is now usable.
* 3 Behavior of search field action buttons can be used.

## [1.0.0] - 2023-10-23
* Test Process achieved successfully

## [1.0.0-beta+2] - 2023-10-20
* Dart Analysis Problems are solved

## [1.0.0-beta+1] - 2023-10-20
* Initial release - hot fixes

## [1.0.0-beta] - 2023-10-20
* Initial release