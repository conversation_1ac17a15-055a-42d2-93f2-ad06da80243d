import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';

import '../misc/utils.dart';

/// 基于手势保存和计算选定的索引。
///
/// 这个类在概念上与UI手势相关联，因此它的方法名称暗示了交互（特别是点击和拖动），
/// 但它只是保存数据并进行一些计算。
///
/// 这种行为不同寻常，但在这种情况下，它有助于保持一切更具教学性，
/// 因为您可以轻松地将UI操作与其在选择方面的结果联系起来。
class SelectionManager {
  /// 拖动开始的索引。
  int get dragStartIndex => _dragStartIndex;
  var _dragStartIndex = -1;

  /// 最后一个被拖动经过的已知索引。
  int get dragEndIndex => _dragEndIndex;
  var _dragEndIndex = -1;

  /// 是否处于取消选择模式（当从已选择的项目开始拖拽时）
  bool _isUnselectingMode = false;

  /// 获取当前选定的索引。
  ///
  /// 索引可以通过[_selectedIndexes]设置器直接选择，
  /// 也可以通过手势选择，使用[startDrag]、[updateDrag]、[endDrag]和[tap]。
  Set<int> get selectedIndexes => UnmodifiableSetView(_selectedIndexes);

  /// 设置当前选定的索引。
  ///
  /// 任何当前活动的拖动操作都将被中断。
  set selectedIndexes(Set<int> selectedIndexes) {
    endDrag(); // 这里会重置 _isUnselectingMode
    _selectedIndexes = Set.of(selectedIndexes);
  }

  var _selectedIndexes = <int>{};

  /// 从[_selectedIndexes]中移除所有索引。
  ///
  /// 任何当前活动的拖动操作都将被中断。
  void clear() {
    endDrag(); // 这里会重置 _isUnselectingMode
    _selectedIndexes.clear();
  }

  /// 将[index]添加到[_selectedIndexes]中，如果已经存在则将其移除。
  void tap(int index) {
    if (_selectedIndexes.contains(index)) {
      _selectedIndexes.remove(index);
    } else {
      _selectedIndexes.add(index);
    }
  }

  /// 开始拖拽操作。
  ///
  /// 如果[index]已经被选中，则进入取消选择模式，否则进入选择模式。
  /// 在取消选择模式下，拖拽途径的所有item都会被取消选择。
  void startDrag(int index) {
    _dragStartIndex = _dragEndIndex = index;

    // 检查开始拖拽的item是否已被选中
    if (_selectedIndexes.contains(index)) {
      // 如果已被选中，进入取消选择模式
      _isUnselectingMode = true;
      // 立即取消选择该item
      _selectedIndexes.remove(index);
    } else {
      // 如果未被选中，进入正常选择模式
      _isUnselectingMode = false;
      _selectedIndexes.add(index);
    }
  }

  /// 更新[_selectedIndexes]，根据[index]、[dragStartIndex]和[dragEndIndex]
  /// 添加/删除一个或多个索引。
  ///
  /// 在以下情况下不执行任何操作：
  ///
  ///   * [index]为负数。
  ///   * 拖动尚未开始。
  void updateDrag(int index) {
    if (index < 0) return;
    if ((_dragStartIndex == -1) || (_dragEndIndex == -1)) return;

    if (_isUnselectingMode) {
      // 在取消选择模式下，处理拖拽途径的所有item

      // 检测是否发生了方向变化（类似于正常选择模式的逻辑）
      if ((index < dragStartIndex) && (index < dragEndIndex) ||
          (index > dragStartIndex) && (index > dragEndIndex)) {
        // 如果方向发生变化，先处理到起始点的路径
        _updateUnselectDragForwardOrBackward(_dragStartIndex);
        _dragEndIndex = _dragStartIndex;
      }

      // 处理当前拖拽路径
      _updateUnselectDragForwardOrBackward(index);
      _dragEndIndex = index;
      return;
    }

    // 正常选择模式的原有逻辑
    // If the drag is both forward and backward, drag to the start index,
    // and then continue the drag, whether it is forward or backward.
    if ((index < dragStartIndex) && (index < dragEndIndex) ||
        (index > dragStartIndex) && (index > dragEndIndex)) {
      _updateDragForwardOrBackward(_dragStartIndex);
      _dragEndIndex = _dragStartIndex;
    }

    _updateDragForwardOrBackward(index);
    _dragEndIndex = index;
  }

  /// 结束当前拖动。
  void endDrag() {
    _dragStartIndex = -1;
    _dragEndIndex = -1;
    _isUnselectingMode = false; // 重置取消选择模式
  }

  /// 更新[_selectedIndexes]，根据[index]、[dragStartIndex]和[dragEndIndex]
  /// 添加/删除一个或多个索引。
  ///
  /// 这不能处理既向前又向后的拖动（反之亦然）。可以通过在拖动时从大于起始索引的索引
  /// 跳转到小于起始索引的索引来实现这一点。
  void _updateDragForwardOrBackward(int index) {
    final indexesDraggedBy = intSetFromRange(index, _dragEndIndex);

    void removeIndexesDraggedByExceptTheCurrent() {
      indexesDraggedBy.remove(index);
      _selectedIndexes.removeAll(indexesDraggedBy);
    }

    final isSelectingForward = index > _dragStartIndex;
    final isSelectingBackward = index < _dragStartIndex;

    if (isSelectingForward) {
      final isUnselecting = index < _dragEndIndex;
      if (isUnselecting) {
        removeIndexesDraggedByExceptTheCurrent();
      } else {
        _selectedIndexes.addAll(indexesDraggedBy);
      }
    } else if (isSelectingBackward) {
      final isUnselecting = index > _dragEndIndex;
      if (isUnselecting) {
        removeIndexesDraggedByExceptTheCurrent();
      } else {
        _selectedIndexes.addAll(indexesDraggedBy);
      }
    } else {
      removeIndexesDraggedByExceptTheCurrent();
    }
  }

  /// 在取消选择模式下更新[_selectedIndexes]，根据[index]、[dragStartIndex]和[dragEndIndex]
  /// 添加/删除一个或多个索引。
  ///
  /// 这个方法与[_updateDragForwardOrBackward]类似，但逻辑相反：
  /// - 在正常拖拽中，它会取消选择索引
  /// - 在反向拖拽中，它会恢复选择索引
  void _updateUnselectDragForwardOrBackward(int index) {
    final indexesDraggedBy = intSetFromRange(index, _dragEndIndex);

    void addIndexesDraggedByExceptTheCurrent() {
      indexesDraggedBy.remove(index);
      _selectedIndexes.addAll(indexesDraggedBy);
    }

    final isUnselectingForward = index > _dragStartIndex;
    final isUnselectingBackward = index < _dragStartIndex;

    if (isUnselectingForward) {
      final isReselecting = index < _dragEndIndex;
      if (isReselecting) {
        addIndexesDraggedByExceptTheCurrent();
      } else {
        _selectedIndexes.removeAll(indexesDraggedBy);
      }
    } else if (isUnselectingBackward) {
      final isReselecting = index > _dragEndIndex;
      if (isReselecting) {
        addIndexesDraggedByExceptTheCurrent();
      } else {
        _selectedIndexes.removeAll(indexesDraggedBy);
      }
    } else {
      addIndexesDraggedByExceptTheCurrent();
    }
  }
}

/// 关于网格选择的信息。
@immutable
class Selection {
  /// 创建一个新的[Selection]。
  Selection(Set<int> selectedIndexes)
      : selectedIndexes = UnmodifiableSetView(Set.of(selectedIndexes));

  /// 创建一个没有选定项目的新[Selection]。
  const Selection.empty() : selectedIndexes = const {};

  /// 被选中的网格索引。
  final Set<int> selectedIndexes;

  /// 选定索引的数量。
  int get amount => selectedIndexes.length;

  /// 网格当前是否处于选择模式。
  bool get isSelecting => amount > 0;

  @override
  String toString() => 'Selection{_selectedIndexes: $selectedIndexes}';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Selection &&
          runtimeType == other.runtimeType &&
          setEquals(selectedIndexes, other.selectedIndexes);

  @override
  int get hashCode => const SetEquality<int>().hash(selectedIndexes);
}
