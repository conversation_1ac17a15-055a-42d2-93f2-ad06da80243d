import 'package:flutter/widgets.dart';

import 'drag_select_grid_view.dart';
import 'selection.dart';

/// 当选择变更时通知的函数签名。
typedef SelectionChangedCallback = void Function(Selection selection);

/// [DragSelectGridView]的控制器。
///
/// 提供可用于更新UI的信息，以指示是否有选中的项目以及选中了多少项。
///
/// 它还允许直接更新选中的项目。
class DragSelectGridViewController extends ValueNotifier<Selection> {
  /// 创建[DragSelectGridView]的控制器。
  ///
  /// 初始选择为[Selection.empty]，除非提供了不同的选择。
  DragSelectGridViewController([Selection? selection])
      : _isSelectionModeEnabled = false,
        super(selection ?? const Selection.empty());

  /// 是否启用选择模式。
  ///
  /// 如果为 false，则不会响应任何选择手势，也不会进入选择模式。
  bool get isSelectionModeEnabled => _isSelectionModeEnabled;
  bool _isSelectionModeEnabled;

  /// 设置选择模式是否启用。
  set isSelectionModeEnabled(bool enabled) {
    if (_isSelectionModeEnabled != enabled) {
      _isSelectionModeEnabled = enabled;
      // 如果禁用选择模式，清除所有选择
      if (!enabled) {
        clear();
      }
      notifyListeners();
    }
  }

  /// 切换选择模式的启用状态。
  void toggleSelectionMode() {
    isSelectionModeEnabled = !isSelectionModeEnabled;
  }

  /// 启用选择模式。
  void enableSelectionMode() {
    isSelectionModeEnabled = true;
  }

  /// 禁用选择模式。
  void disableSelectionMode() {
    isSelectionModeEnabled = false;
  }

  /// 清除网格选择。
  void clear() => value = const Selection.empty();
}
