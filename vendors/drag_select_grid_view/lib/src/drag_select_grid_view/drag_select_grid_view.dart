import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/widgets.dart' hide SelectionChangedCallback;

import '../auto_scroll/auto_scroller_mixin.dart';
import '../drag_select_grid_view/selectable.dart';
import 'drag_select_grid_view_controller.dart';
import 'selection.dart';

/// 用于基于索引和是否被选中状态创建小部件的函数签名。
typedef SelectableWidgetBuilder = Widget Function(
  BuildContext context,
  int index,
  bool selected,
);

/// 支持拖动和点击选择项目的网格。
///
/// 默认情况下，长按启用选择。用户可以通过点击来选择/取消选择任何项目。
/// 拖动允许级联选择/取消选择。标志[triggerSelectionOnTap]允许通过点击启用选择。
///
/// 通过自动滚动，此小部件添加了选择超出屏幕边界的项目的能力，而无需停止拖动。
/// 为此，此小部件创建了两个虚拟区域，如果在拖动时指针到达这些区域，
/// 则会触发自动滚动。
///
/// 第一个区域在顶部，触发向后自动滚动。
/// 第二个区域在底部，触发向前自动滚动。
class DragSelectGridView extends StatefulWidget {
  /// 启用自动滚动的热点区域的默认高度。
  static const defaultAutoScrollHotspotHeight = 64.0;

  /// 创建一个支持拖动和点击选择项目的网格。
  ///
  /// 可以通过指定[autoScrollHotspotHeight]来自定义启用自动滚动的热点区域的高度。
  ///
  /// [gridController]提供的信息可用于更新UI，以指示是否有选定的项目以及选定了多少项目，
  /// 此外还允许直接更新选定的项目。
  ///
  /// 通过将[unselectOnWillPop]设为false，当用户尝试弹出路由时，项目不会被取消选择。
  ///
  /// 必须使用[itemBuilder]根据索引和是否被选中来创建小部件。
  ///
  /// 有关其他参数条款的信息，请参阅[GridView.builder]。
  DragSelectGridView({
    Key? key,
    double? autoScrollHotspotHeight,
    ScrollController? scrollController,
    this.gridController,
    this.triggerSelectionOnTap = false,
    this.reverse = false,
    this.primary,
    this.physics,
    this.shrinkWrap = false,
    this.padding,
    required this.gridDelegate,
    required this.itemBuilder,
    this.itemCount,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
    this.cacheExtent,
    this.semanticChildCount,
    this.dragStartBehavior = DragStartBehavior.start,
    this.keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    this.restorationId,
    this.clipBehavior = Clip.hardEdge,
    this.isSelectionModeEnabled = true,
  })  : autoScrollHotspotHeight =
            autoScrollHotspotHeight ?? defaultAutoScrollHotspotHeight,
        scrollController = scrollController ?? ScrollController(),
        super(key: key);

  /// 启用自动滚动的热点区域的高度。
  ///
  /// 此值用于顶部和底部热点区域。宽度将与小部件的宽度匹配。
  ///
  /// 默认为[defaultAutoScrollHotspotHeight]。
  final double autoScrollHotspotHeight;

  /// 参考 [ScrollView.controller]。
  final ScrollController scrollController;

  /// 网格的控制器。
  ///
  /// 提供可用于更新UI的信息，以指示是否有选定的项目以及选定了多少项目。
  ///
  /// 还允许直接更新选定的项目。
  ///
  /// 此控制器在[DragSelectGridViewState]销毁后可能无法使用，
  /// 因为[DragSelectGridViewController.dispose]将被调用，
  /// 监听器将被清理。
  final DragSelectGridViewController? gridController;

  /// 是否应该通过点击而不是长按来开始选择。
  ///
  /// 默认为false。
  final bool triggerSelectionOnTap;

  /// 参考 [ScrollView.reverse]。
  final bool reverse;

  /// 参考 [ScrollView.primary]。
  final bool? primary;

  /// 参考 [ScrollView.physics]。
  final ScrollPhysics? physics;

  /// 参考 [ScrollView.shrinkWrap]。
  final bool shrinkWrap;

  /// 参考 [BoxScrollView.padding]。
  final EdgeInsetsGeometry? padding;

  /// 参考 [GridView.gridDelegate]。
  final SliverGridDelegate gridDelegate;

  /// 每当需要构建子项时调用。
  ///
  /// 客户端应该使用此方法根据索引和是否被选中状态动态构建子项。
  ///
  /// 另请参考 [SliverChildBuilderDelegate.builder]。
  final SelectableWidgetBuilder itemBuilder;

  /// 参考 [SliverChildBuilderDelegate.childCount]。
  final int? itemCount;

  /// 参考 [SliverChildBuilderDelegate.addAutomaticKeepAlives]。
  final bool addAutomaticKeepAlives;

  /// 参考 [SliverChildBuilderDelegate.addRepaintBoundaries]。
  final bool addRepaintBoundaries;

  /// 参考 [SliverChildBuilderDelegate.addSemanticIndexes]。
  final bool addSemanticIndexes;

  /// 参考 [ScrollView.cacheExtent]。
  final double? cacheExtent;

  /// 参考 [ScrollView.semanticChildCount]。
  final int? semanticChildCount;

  /// 参考 [ScrollView.dragStartBehavior]。
  final DragStartBehavior dragStartBehavior;

  /// 参考 [ScrollView.keyboardDismissBehavior]。
  final ScrollViewKeyboardDismissBehavior keyboardDismissBehavior;

  /// 参考 [ScrollView.restorationId]。
  final String? restorationId;

  /// 参考 [ScrollView.clipBehavior]。
  final Clip clipBehavior;

  /// 是否启用选择模式。
  ///
  /// 如果为 false，则不会响应任何选择手势，也不会进入选择模式。
  /// 默认为 true。
  final bool isSelectionModeEnabled;

  @override
  DragSelectGridViewState createState() => DragSelectGridViewState();
}

/// 支持拖动和点击选择项目的网格的状态。
@visibleForTesting
class DragSelectGridViewState extends State<DragSelectGridView>
    with AutoScrollerMixin<DragSelectGridView> {
  final _elements = <SelectableElement>{};
  final _selectionManager = SelectionManager();
  LongPressMoveUpdateDetails? _lastMoveUpdateDetails;

  DragSelectGridViewController? get _gridController => widget.gridController;

  /// 通过拖动或点击选择的索引。
  Set<int> get selectedIndexes => _selectionManager.selectedIndexes;

  /// 是否有任何项目被选中。
  bool get isSelecting => selectedIndexes.isNotEmpty;

  /// 是否正在执行拖动手势。
  bool get isDragging =>
      (_selectionManager.dragStartIndex != -1) &&
      (_selectionManager.dragEndIndex != -1);

  @override
  double get autoScrollHotspotHeight => widget.autoScrollHotspotHeight;

  @override
  ScrollController get scrollController => widget.scrollController;

  @override
  void handleScroll() {
    final details = _lastMoveUpdateDetails;
    if (details != null) _handleDragUpdate(details);
  }

  @override
  void initState() {
    super.initState();
    final controller = _gridController;
    if (controller != null) {
      controller.addListener(_onSelectionChanged);
      _selectionManager.selectedIndexes = controller.value.selectedIndexes;
      // 确保 controller 的 isSelectionModeEnabled 与 widget 的 isSelectionModeEnabled 一致
      controller.isSelectionModeEnabled = widget.isSelectionModeEnabled;
    }
  }

  @override
  void didUpdateWidget(DragSelectGridView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当 widget 的 isSelectionModeEnabled 属性发生变化时，更新 controller
    if (oldWidget.isSelectionModeEnabled != widget.isSelectionModeEnabled) {
      _gridController?.isSelectionModeEnabled = widget.isSelectionModeEnabled;
    }
  }

  @override
  void dispose() {
    _gridController?.removeListener(_onSelectionChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
      onTapUp: _handleTapUp,
      onHorizontalDragStart: _handleHorizontalDragStart,
      onHorizontalDragUpdate: _handleHorizontalDragUpdate,
      onHorizontalDragEnd: _handleHorizontalDragEnd,
      onLongPressStart: _handleLongPressStart,
      onLongPressMoveUpdate: _handleLongPressMoveUpdate,
      onLongPressEnd: _handleLongPressEnd,
      behavior: HitTestBehavior.translucent,
      child: IgnorePointer(
        ignoring: isDragging,
        child: GridView.builder(
          // 暂时不传递 controller
          // controller: widget.scrollController,
          reverse: widget.reverse,
          primary: widget.primary,
          physics: widget.physics,
          shrinkWrap: widget.shrinkWrap,
          padding: widget.padding,
          gridDelegate: widget.gridDelegate,
          itemCount: widget.itemCount,
          addAutomaticKeepAlives: widget.addAutomaticKeepAlives,
          addRepaintBoundaries: widget.addRepaintBoundaries,
          addSemanticIndexes: widget.addSemanticIndexes,
          cacheExtent: widget.cacheExtent,
          semanticChildCount: widget.semanticChildCount,
          dragStartBehavior: widget.dragStartBehavior,
          keyboardDismissBehavior: widget.keyboardDismissBehavior,
          restorationId: widget.restorationId,
          clipBehavior: widget.clipBehavior,
          itemBuilder: (context, index) {
            return IgnorePointer(
              ignoring: widget.isSelectionModeEnabled &&
                  (isSelecting || widget.triggerSelectionOnTap),
              child: Selectable(
                index: index,
                onMountElement: _elements.add,
                onUnmountElement: _elements.remove,
                child: widget.itemBuilder(
                  context,
                  index,
                  selectedIndexes.contains(index),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  void _onSelectionChanged() {
    final controller = _gridController;
    if (controller != null) {
      final controllerSelectedIndexes = controller.value.selectedIndexes;
      if (!setEquals(controllerSelectedIndexes, selectedIndexes)) {
        _selectionManager.selectedIndexes = controllerSelectedIndexes;
      }
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.isSelectionModeEnabled) return;

    if (isSelecting || widget.triggerSelectionOnTap) {
      final tapIndex = _findIndexOfSelectable(details.localPosition);

      if (tapIndex != -1) {
        setState(() => _selectionManager.tap(tapIndex));
        _notifySelectionChange();
      }
    }
  }

  void _handleLongPressStart(LongPressStartDetails details) {
    if (!widget.isSelectionModeEnabled) return;

    final pressIndex = _findIndexOfSelectable(details.localPosition);

    if (pressIndex != -1) {
      setState(() => _selectionManager.startDrag(pressIndex));
      _notifySelectionChange();
    }
  }

  void _handleLongPressMoveUpdate(LongPressMoveUpdateDetails details) {
    if (!isDragging) return;

    _handleDragUpdate(details);
  }

  void _handleLongPressEnd(LongPressEndDetails details) {
    setState(_selectionManager.endDrag);
    stopScrolling();
  }

  void _handleHorizontalDragStart(DragStartDetails details) {
    if (!widget.isSelectionModeEnabled) return;

    final dragIndex = _findIndexOfSelectable(details.localPosition);

    if (dragIndex != -1) {
      setState(() => _selectionManager.startDrag(dragIndex));
      _notifySelectionChange();
    }
  }

  void _handleHorizontalDragUpdate(DragUpdateDetails details) {
    if (!isDragging) return;

    // 创建一个类似于 LongPressMoveUpdateDetails 的对象，以便复用现有的处理逻辑
    final longPressDetails = LongPressMoveUpdateDetails(
      globalPosition: details.globalPosition,
      localPosition: details.localPosition,
      offsetFromOrigin: details.delta,
      localOffsetFromOrigin: details.localPosition,
    );

    _handleDragUpdate(longPressDetails);
  }

  void _handleDragUpdate(LongPressMoveUpdateDetails details) {
    _lastMoveUpdateDetails = details;
    final dragIndex = _findIndexOfSelectable(details.localPosition);

    if ((dragIndex != -1) && (dragIndex != _selectionManager.dragEndIndex)) {
      setState(() => _selectionManager.updateDrag(dragIndex));
      _notifySelectionChange();
    }

    if (isInsideUpperAutoScrollHotspot(details.localPosition)) {
      if (widget.reverse) {
        startAutoScrollingForward();
      } else {
        startAutoScrollingBackward();
      }
    } else if (isInsideLowerAutoScrollHotspot(details.localPosition)) {
      if (widget.reverse) {
        startAutoScrollingBackward();
      } else {
        startAutoScrollingForward();
      }
    } else {
      stopScrolling();
    }
  }

  void _handleHorizontalDragEnd(DragEndDetails details) {
    setState(_selectionManager.endDrag);
    stopScrolling();
  }

  int _findIndexOfSelectable(Offset offset) {
    final ancestor = context.findRenderObject();
    var elementFinder = Set.of(_elements).firstWhereOrNull;

    // Conceptually, `Set.singleWhere()` is the safer option, however we're
    // avoiding to iterate over the whole `Set` to improve the performance.
    assert(() {
      elementFinder = Set.of(_elements).singleWhereOrNull;
      return true;
    }());

    final element = elementFinder(
      (element) => element.containsOffset(ancestor, offset),
    );

    return (element == null) ? -1 : element.widget.index;
  }

  void _notifySelectionChange() {
    _gridController?.value = Selection(_selectionManager.selectedIndexes);
  }
}
