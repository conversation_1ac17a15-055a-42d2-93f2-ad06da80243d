import 'package:flutter/widgets.dart';

/// 当元素被挂载或卸载时进行通知的函数签名。
typedef ElementUpdateCallback = void Function(SelectableElement);

/// 帮助跟踪网格项的元素。
///
/// 提供允许存储元素的回调，以便可以使用方法
/// [SelectableElement.containsOffset] 来确定给定偏移量处
/// 有哪个网格项。
class Selectable extends ProxyWidget {
  /// 创建一个 [Selectable]。
  const Selectable({
    Key? key,
    required this.index,
    required this.onMountElement,
    required this.onUnmountElement,
    required Widget child,
  }) : super(key: key, child: child);

  /// 网格内的小部件索引。
  final int index;

  /// 当元素被挂载时通知的回调。
  final ElementUpdateCallback onMountElement;

  /// 当元素被卸载时通知的回调。
  final ElementUpdateCallback onUnmountElement;

  @override
  SelectableElement createElement() => SelectableElement(this);
}

/// 帮助跟踪网格项元素的小部件的元素。
class SelectableElement extends ProxyElement {
  /// 为帮助跟踪网格项元素的小部件创建元素。
  SelectableElement(Selectable widget) : super(widget);

  @override
  Selectable get widget => super.widget as Selectable;

  @override
  void mount(Element? parent, dynamic newSlot) {
    super.mount(parent, newSlot);
    widget.onMountElement.call(this);
  }

  @override
  void unmount() {
    widget.onUnmountElement.call(this);
    super.unmount();
  }

  /// [offset] 是否在此元素的边界内。
  bool containsOffset(RenderObject? ancestor, Offset offset) {
    final box = renderObject as RenderBox;
    final rect = box.localToGlobal(Offset.zero, ancestor: ancestor) & box.size;
    return rect.contains(offset);
  }

  @override
  void notifyClients(ProxyWidget oldWidget) {}
}
