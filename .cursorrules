3. 该项目的 UI/UX 必须遵循 IOS 原生的 Cupertino 风格，请尽量模仿 IOS 的风格。
4. 你不需要使用 flutter run 或 flutter dev 命令，因为我已经启动了调试模式。
5. 项目中请全程使用 getx，并且让项目结构符合 getx 项目的规范。
5. 为了项目的可读性和可维护性，请尽可能地进行封装，不要让一个 dart 文件里有太多代码。
6. 使用 toastification.show 的时候，请不要传入 context 参数。

项目简介：基于 `Flutter3 + getx + hive + drift` 的图片管理、图片处理 APP。

数据库的 ORM 使用 drift。

项目结构：
lib/                    # 主要代码目录
├── common/             # 公共代码
│   ├── api/            # API接口
│   ├── binding/        # GetX 绑定
│   ├── controllers/    # GetX 控制器
│   ├── hive/           # Hive 数据相关
│   ├── middlewares/    # GetX 路由中间件
│   ├── models/         # 数据模型
│   ├── routes/         # GetX 路由管理
│   ├── services/       # 数据库的业务
│   ├── database/       # 数据库
│   └── utils/          # 工具类
├── pages/              # 页面
├── widgets/            # 自定义通用组件
└── main.dart           # 应用入口