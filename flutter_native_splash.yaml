flutter_native_splash:
  # 这个包生成原生代码，用于自定义 Flutter 默认的白色原生启动屏幕，
  # 可设置背景颜色和启动图像。
  # 自定义下面的参数，然后在终端中运行以下命令：
  # dart run flutter_native_splash:create
  # 要恢复 Flutter 默认的白色启动屏幕，请在终端中运行以下命令：
  # dart run flutter_native_splash:remove

  # 重要提示：这些参数不影响 Android 12 及更高版本的配置，因为
  # 这些版本处理启动屏幕的方式与之前的 Android 版本不同。Android 12 及更高版本
  # 必须在下面的 android_12 部分中专门配置。

  # color 或 background_image 是唯一必需的参数。使用 color 将启动屏幕的背景
  # 设置为纯色。使用 background_image 将启动屏幕的背景设置为 png 图像。
  # 这对渐变很有用。图像将被拉伸到应用程序的大小。只能使用一个参数，color 和 background_image 不能同时设置。
  color: "#fe799f"
  #background_image: "assets/background.png"

  # 可选参数列在下面。要启用参数，请通过删除开头的 # 字符来取消注释。

  # image 参数允许您指定在启动屏幕中使用的图像。它必须是 png 文件，
  # 并且应该为 4x 像素密度调整大小。
  image: assets/splash/splash_en.png

  # branding 属性允许您指定在启动屏幕中用作品牌的图像。
  # 它必须是 png 文件。适用于 Android、iOS 和 Web。对于 Android 12，
  # 请参阅下面的 Android 12 部分。
  #branding: assets/dart.png

  # 要将品牌图像定位在屏幕底部，您可以使用 bottom、bottomRight 
  # 和 bottomLeft。如果未指定或指定了其他内容，默认值为 bottom。
  #branding_mode: bottom

  # 设置品牌图像距离屏幕底部的填充。默认值为 0
  # (Web 上暂不支持)
  # branding_bottom_padding: 24

  # color_dark、background_image_dark、image_dark、branding_dark 是设置背景
  # 和图像的参数，当设备处于深色模式时使用。如果未指定这些参数，应用将使用
  # 上面的参数。如果上面没有参数，应用将使用浅色模式的值。
  # 如果指定了 image_dark 参数，必须指定 color_dark 或 background_image_dark。
  # color_dark 和 background_image_dark 不能同时设置。
  color_dark: "#000000"
  #background_image_dark: "assets/dark-background.png"
  image_dark: assets/splash/splash_en_pink.png
  #branding_dark: assets/dart_dark.png

  # 从 Android 12 开始，启动屏幕的处理方式与之前版本不同。
  # 请访问 https://developer.android.com/guide/topics/ui/splash-screen
  # 以下是 Android 12+ 的特定参数。
  android_12:
    # image 参数设置启动屏幕图标图像。如果未指定此参数，
    # 将使用应用的启动器图标代替。
    # 请注意，启动屏幕将被裁剪为屏幕中心的圆形。
    # 带有图标背景的应用图标：应为 960×960 像素，并适合直径为 640 像素的圆形。
    # 没有图标背景的应用图标：应为 1152×1152 像素，并适合直径为 768 像素的圆形。
    # 要使 1152x1152 的图像适合直径为 768 的圆形，只需确保
    # 图像中最重要的设计元素放置在 1152x1152 画布中心直径为 768 的圆形区域内。
    image: assets/splash/splash_en.png

    # 启动屏幕背景颜色。
    color: "#fe799f"

    # 应用图标背景颜色。
    #icon_background_color: "#111111"

    # branding 属性允许您指定在启动屏幕中用作品牌的图像。
    #branding: assets/dart.png

    # image_dark、color_dark、icon_background_color_dark 和 branding_dark 设置的值
    # 在设备处于深色模式时应用。如果未指定，应用将使用
    # 上面的参数。如果上面没有参数，应用将使用浅色模式的值。
    image_dark: assets/splash/splash_en_pink.png
    color_dark: "#000000"
    # icon_background_color_dark: "#eeeeee"

  # android、ios 和 web 参数可用于在指定平台上禁用生成启动屏幕。
  #android: false
  #ios: false
  #web: false

  # 可以使用以下参数指定平台特定的图像，这将覆盖
  # 相应的参数。您可以指定所有、选定或不指定这些参数：
  #color_android: "#42a5f5"
  #color_dark_android: "#042a49"
  #color_ios: "#42a5f5"
  #color_dark_ios: "#042a49"
  #color_web: "#42a5f5"
  #color_dark_web: "#042a49"
  #image_android: assets/splash-android.png
  #image_dark_android: assets/splash-invert-android.png
  #image_ios: assets/splash-ios.png
  #image_dark_ios: assets/splash-invert-ios.png
  #image_web: assets/splash-web.gif
  #image_dark_web: assets/splash-invert-web.gif
  #background_image_android: "assets/background-android.png"
  #background_image_dark_android: "assets/dark-background-android.png"
  #background_image_ios: "assets/background-ios.png"
  #background_image_dark_ios: "assets/dark-background-ios.png"
  #background_image_web: "assets/background-web.png"
  #background_image_dark_web: "assets/dark-background-web.png"
  #branding_android: assets/brand-android.png
  #branding_bottom_padding_android: 24
  #branding_dark_android: assets/dart_dark-android.png
  #branding_ios: assets/brand-ios.png
  #branding_bottom_padding_ios: 24
  #branding_dark_ios: assets/dart_dark-ios.png
  #branding_web: assets/brand-web.gif
  #branding_dark_web: assets/dart_dark-web.gif

  # 启动图像的位置可以通过 android_gravity、ios_content_mode 和
  # web_image_mode 参数设置。所有默认值为 center。
  #
  # android_gravity 可以是以下 Android Gravity 之一（参见
  # https://developer.android.com/reference/android/view/Gravity）：bottom、center、
  # center_horizontal、center_vertical、clip_horizontal、clip_vertical、end、fill、fill_horizontal、
  # fill_vertical、left、right、start 或 top。android_gravity 可以使用 | 运算符组合以实现多种效果。
  # 例如：
  # `android_gravity: fill|clip_vertical` - 这将填充宽度，同时保持图像的垂直纵横比
  #android_gravity: center
  #
  # ios_content_mode 可以是以下 iOS UIView.ContentMode 之一（参见
  # https://developer.apple.com/documentation/uikit/uiview/contentmode）：scaleToFill、
  # scaleAspectFit、scaleAspectFill、center、top、bottom、left、right、topLeft、topRight、
  # bottomLeft 或 bottomRight。
  #ios_content_mode: center
  #
  # web_image_mode 可以是以下模式之一：center、contain、stretch 和 cover。
  #web_image_mode: center

  # 可以使用 android_screen_orientation 参数在 Android 中设置屏幕方向。
  # 有效参数可以在这里找到：
  # https://developer.android.com/guide/topics/manifest/activity-element#screen
  #android_screen_orientation: sensorLandscape

  # 要隐藏通知栏，请使用 fullscreen 参数。在 web 上无效，因为 web
  # 没有通知栏。默认为 false。
  # 注意：与 Android 不同，iOS 不会在应用加载时自动显示通知栏。
  #      要显示通知栏，请将以下代码添加到您的 Flutter 应用：
  #      WidgetsFlutterBinding.ensureInitialized();
  #      SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.bottom, SystemUiOverlay.top], );
  # fullscreen: true

  # 如果您已更改 info.plist 文件的名称，可以使用 info_plist_files
  # 参数指定文件名。仅删除下面三行中的 # 字符，
  # 不要删除任何空格：
  #info_plist_files:
  #  - 'ios/Runner/Info-Debug.plist'
  #  - 'ios/Runner/Info-Release.plist'